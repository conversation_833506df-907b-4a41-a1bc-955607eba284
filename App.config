<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <!-- Configuration des chaînes de connexion -->
  <connectionStrings>
    <!-- Chaîne de connexion MySQL pour la base de données du supermarché -->
    <add name="SupermarketDB" 
         connectionString="Server=localhost;Port=3306;Database=supermarket_db;Uid=root;Pwd=;Connection Timeout=30;Command Timeout=60;CharSet=utf8mb4;SslMode=None;AllowUserVariables=True;" 
         providerName="MySql.Data.MySqlClient" />
  </connectionStrings>

  <!-- Configuration de l'application -->
  <appSettings>
    <!-- Informations générales de l'application -->
    <add key="ApplicationName" value="Système de Gestion Supermarché" />
    <add key="Version" value="1.0.0" />
    <add key="DefaultLanguage" value="fr-DZ" />
    <add key="DefaultCurrency" value="DZD" />
    <add key="DefaultTVARate" value="19.00" />

    <!-- Configuration de la base de données -->
    <add key="DB_Server" value="localhost" />
    <add key="DB_Port" value="3306" />
    <add key="DB_Database" value="supermarket_db" />
    <add key="DB_Username" value="root" />
    <add key="DB_Password" value="" />
    <add key="DB_ConnectionTimeout" value="30" />
    <add key="DB_CommandTimeout" value="60" />

    <!-- Répertoires de l'application -->
    <add key="BackupDirectory" value="" />
    <add key="ReportsDirectory" value="" />
    <add key="ProductImagesDirectory" value="" />
    <add key="CompanyLogosDirectory" value="" />

    <!-- Paramètres d'interface utilisateur -->
    <add key="DefaultPageSize" value="50" />
    <add key="SessionTimeoutMinutes" value="480" />

    <!-- Paramètres de fonctionnalité -->
    <add key="EnableDebugLogging" value="false" />
    <add key="EnableAutoBackup" value="true" />
    <add key="AutoBackupIntervalHours" value="24" />

    <!-- Paramètres de sécurité -->
    <add key="MinPasswordLength" value="6" />
    <add key="RequirePasswordComplexity" value="false" />
    <add key="MaxLoginAttempts" value="5" />
    <add key="LockoutDurationMinutes" value="30" />

    <!-- Paramètres d'impression -->
    <add key="DefaultPrinterName" value="" />
    <add key="ReceiptPrinterName" value="" />
    <add key="EnableThermalPrinting" value="true" />
    <add key="ReceiptWidth" value="80" />

    <!-- Paramètres de facturation -->
    <add key="InvoiceNumberPrefix" value="FV" />
    <add key="PurchaseNumberPrefix" value="FA" />
    <add key="ProformaNumberPrefix" value="PF" />
    <add key="DeliveryNotePrefix" value="BL" />
    <add key="AutoGenerateInvoiceNumbers" value="true" />

    <!-- Paramètres de stock -->
    <add key="EnableStockAlerts" value="true" />
    <add key="DefaultStockAlertThreshold" value="10" />
    <add key="EnableNegativeStock" value="false" />
    <add key="AutoUpdatePMP" value="true" />

    <!-- Paramètres de caisse -->
    <add key="EnableCashDrawer" value="false" />
    <add key="CashDrawerPort" value="COM1" />
    <add key="EnableBarcodeScanner" value="true" />
    <add key="BarcodeScannerPort" value="COM2" />

    <!-- Paramètres de sauvegarde -->
    <add key="BackupRetentionDays" value="30" />
    <add key="CompressBackups" value="true" />
    <add key="BackupOnExit" value="true" />

    <!-- Paramètres de rapport -->
    <add key="DefaultReportFormat" value="PDF" />
    <add key="EnableReportScheduling" value="false" />
    <add key="ReportEmailSMTP" value="" />
    <add key="ReportEmailPort" value="587" />
    <add key="ReportEmailUsername" value="" />
    <add key="ReportEmailPassword" value="" />

    <!-- Paramètres de performance -->
    <add key="EnableQueryCaching" value="true" />
    <add key="CacheExpirationMinutes" value="15" />
    <add key="MaxConcurrentUsers" value="10" />

    <!-- Paramètres de localisation (Algérie) -->
    <add key="CountryCode" value="DZ" />
    <add key="CurrencySymbol" value="د.ج" />
    <add key="DateFormat" value="dd/MM/yyyy" />
    <add key="TimeFormat" value="HH:mm" />
    <add key="NumberDecimalSeparator" value="," />
    <add key="NumberGroupSeparator" value=" " />
  </appSettings>

  <!-- Configuration du runtime -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="MySql.Data" publicKeyToken="c5687fc88969c44d" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.33.0" newVersion="8.0.33.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Dapper" publicKeyToken="684a7d7421bf6a8e" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>

  <!-- Configuration de démarrage -->
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>

  <!-- Configuration du logging (si NLog est utilisé) -->
  <configSections>
    <section name="nlog" type="NLog.Config.ConfigSectionHandler, NLog" />
  </configSections>

  <nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <targets>
      <target xsi:type="File" name="fileTarget"
              fileName="${basedir}/Logs/${shortdate}.log"
              layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}" />
      <target xsi:type="Console" name="consoleTarget"
              layout="${time} ${level:uppercase=true} ${message}" />
    </targets>
    <rules>
      <logger name="*" minlevel="Info" writeTo="fileTarget" />
      <logger name="*" minlevel="Debug" writeTo="consoleTarget" />
    </rules>
  </nlog>
</configuration>
