using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;
using SupermarketManagement.Data;
using SupermarketManagement.Models;

namespace SupermarketManagement.Repositories
{
    /// <summary>
    /// Repository de base avec implémentation commune
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public abstract class BaseRepository<T> : IPagedRepository<T>, ITransactionalRepository, IDisposable 
        where T : BaseEntity
    {
        protected readonly DapperContext _context;
        protected readonly string _tableName;
        private bool _disposed = false;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="context">Contexte <PERSON></param>
        /// <param name="tableName">Nom de la table</param>
        protected BaseRepository(DapperContext context, string tableName)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _tableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
        }

        /// <summary>
        /// Obtient toutes les entités
        /// </summary>
        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            var sql = $"SELECT * FROM {_tableName} ORDER BY id";
            return await _context.QueryAsync<T>(sql);
        }

        /// <summary>
        /// Obtient une entité par son ID
        /// </summary>
        public virtual async Task<T> GetByIdAsync(int id)
        {
            var sql = $"SELECT * FROM {_tableName} WHERE id = @Id";
            return await _context.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
        }

        /// <summary>
        /// Ajoute une nouvelle entité
        /// </summary>
        public virtual async Task<int> AddAsync(T entity)
        {
            entity.DateCreation = DateTime.Now;
            entity.DateModification = DateTime.Now;

            var insertSql = GenerateInsertSql();
            var id = await _context.ExecuteScalarAsync<int>(insertSql, entity);
            entity.Id = id;
            return id;
        }

        /// <summary>
        /// Met à jour une entité existante
        /// </summary>
        public virtual async Task<bool> UpdateAsync(T entity)
        {
            entity.DateModification = DateTime.Now;

            var updateSql = GenerateUpdateSql();
            var affectedRows = await _context.ExecuteAsync(updateSql, entity);
            return affectedRows > 0;
        }

        /// <summary>
        /// Supprime une entité par son ID
        /// </summary>
        public virtual async Task<bool> DeleteAsync(int id)
        {
            var sql = $"DELETE FROM {_tableName} WHERE id = @Id";
            var affectedRows = await _context.ExecuteAsync(sql, new { Id = id });
            return affectedRows > 0;
        }

        /// <summary>
        /// Vérifie si une entité existe
        /// </summary>
        public virtual async Task<bool> ExistsAsync(int id)
        {
            var sql = $"SELECT COUNT(1) FROM {_tableName} WHERE id = @Id";
            var count = await _context.ExecuteScalarAsync<int>(sql, new { Id = id });
            return count > 0;
        }

        /// <summary>
        /// Compte le nombre total d'entités
        /// </summary>
        public virtual async Task<int> CountAsync()
        {
            var sql = $"SELECT COUNT(*) FROM {_tableName}";
            return await _context.ExecuteScalarAsync<int>(sql);
        }

        /// <summary>
        /// Obtient une page d'entités
        /// </summary>
        public virtual async Task<PagedResult<T>> GetPagedAsync(int pageNumber, int pageSize)
        {
            var offset = (pageNumber - 1) * pageSize;
            
            var countSql = $"SELECT COUNT(*) FROM {_tableName}";
            var totalCount = await _context.ExecuteScalarAsync<int>(countSql);

            var dataSql = $"SELECT * FROM {_tableName} ORDER BY id LIMIT @PageSize OFFSET @Offset";
            var data = await _context.QueryAsync<T>(dataSql, new { PageSize = pageSize, Offset = offset });

            return new PagedResult<T>
            {
                Data = data,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        /// <summary>
        /// Recherche des entités avec pagination
        /// </summary>
        public virtual async Task<PagedResult<T>> SearchPagedAsync(string searchTerm, int pageNumber, int pageSize)
        {
            var offset = (pageNumber - 1) * pageSize;
            var searchColumns = GetSearchColumns();
            
            if (searchColumns.Length == 0)
            {
                return await GetPagedAsync(pageNumber, pageSize);
            }

            var whereClause = string.Join(" OR ", Array.ConvertAll(searchColumns, col => $"{col} LIKE @SearchTerm"));
            var searchParam = $"%{searchTerm}%";

            var countSql = $"SELECT COUNT(*) FROM {_tableName} WHERE {whereClause}";
            var totalCount = await _context.ExecuteScalarAsync<int>(countSql, new { SearchTerm = searchParam });

            var dataSql = $"SELECT * FROM {_tableName} WHERE {whereClause} ORDER BY id LIMIT @PageSize OFFSET @Offset";
            var data = await _context.QueryAsync<T>(dataSql, new { SearchTerm = searchParam, PageSize = pageSize, Offset = offset });

            return new PagedResult<T>
            {
                Data = data,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        /// <summary>
        /// Commence une transaction
        /// </summary>
        public virtual async Task<IDbTransaction> BeginTransactionAsync()
        {
            return await _context.BeginTransactionAsync();
        }

        /// <summary>
        /// Valide une transaction
        /// </summary>
        public virtual Task CommitTransactionAsync(IDbTransaction transaction)
        {
            transaction?.Commit();
            return Task.CompletedTask;
        }

        /// <summary>
        /// Annule une transaction
        /// </summary>
        public virtual Task RollbackTransactionAsync(IDbTransaction transaction)
        {
            transaction?.Rollback();
            return Task.CompletedTask;
        }

        /// <summary>
        /// Génère le SQL d'insertion (à implémenter dans les classes dérivées)
        /// </summary>
        protected abstract string GenerateInsertSql();

        /// <summary>
        /// Génère le SQL de mise à jour (à implémenter dans les classes dérivées)
        /// </summary>
        protected abstract string GenerateUpdateSql();

        /// <summary>
        /// Obtient les colonnes de recherche (à implémenter dans les classes dérivées)
        /// </summary>
        protected abstract string[] GetSearchColumns();

        /// <summary>
        /// Libère les ressources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Libère les ressources
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _context?.Dispose();
                }
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Repository de base pour les entités activables
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public abstract class ActivatableRepository<T> : BaseRepository<T>, IActivatableRepository<T> 
        where T : BaseEntity, IActivatable
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        protected ActivatableRepository(DapperContext context, string tableName) 
            : base(context, tableName)
        {
        }

        /// <summary>
        /// Obtient toutes les entités actives
        /// </summary>
        public virtual async Task<IEnumerable<T>> GetActiveAsync()
        {
            var sql = $"SELECT * FROM {_tableName} WHERE actif = 1 ORDER BY id";
            return await _context.QueryAsync<T>(sql);
        }

        /// <summary>
        /// Active ou désactive une entité
        /// </summary>
        public virtual async Task<bool> SetActiveAsync(int id, bool active)
        {
            var sql = $"UPDATE {_tableName} SET actif = @Active, date_modification = @DateModification WHERE id = @Id";
            var affectedRows = await _context.ExecuteAsync(sql, new 
            { 
                Id = id, 
                Active = active, 
                DateModification = DateTime.Now 
            });
            return affectedRows > 0;
        }

        /// <summary>
        /// Recherche des entités actives avec pagination
        /// </summary>
        public virtual async Task<PagedResult<T>> SearchActivePagedAsync(string searchTerm, int pageNumber, int pageSize)
        {
            var offset = (pageNumber - 1) * pageSize;
            var searchColumns = GetSearchColumns();
            
            if (searchColumns.Length == 0)
            {
                var sql = $"SELECT * FROM {_tableName} WHERE actif = 1 ORDER BY id LIMIT @PageSize OFFSET @Offset";
                var countSql = $"SELECT COUNT(*) FROM {_tableName} WHERE actif = 1";
                
                var totalCount = await _context.ExecuteScalarAsync<int>(countSql);
                var data = await _context.QueryAsync<T>(sql, new { PageSize = pageSize, Offset = offset });

                return new PagedResult<T>
                {
                    Data = data,
                    TotalCount = totalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };
            }

            var whereClause = string.Join(" OR ", Array.ConvertAll(searchColumns, col => $"{col} LIKE @SearchTerm"));
            var searchParam = $"%{searchTerm}%";

            var countSqlWithSearch = $"SELECT COUNT(*) FROM {_tableName} WHERE actif = 1 AND ({whereClause})";
            var totalCountWithSearch = await _context.ExecuteScalarAsync<int>(countSqlWithSearch, new { SearchTerm = searchParam });

            var dataSqlWithSearch = $"SELECT * FROM {_tableName} WHERE actif = 1 AND ({whereClause}) ORDER BY id LIMIT @PageSize OFFSET @Offset";
            var dataWithSearch = await _context.QueryAsync<T>(dataSqlWithSearch, new { SearchTerm = searchParam, PageSize = pageSize, Offset = offset });

            return new PagedResult<T>
            {
                Data = dataWithSearch,
                TotalCount = totalCountWithSearch,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
    }
}
