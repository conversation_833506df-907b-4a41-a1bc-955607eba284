using System;
using System.Data;
using MySql.Data.MySqlClient;
using SupermarketManagement.Configuration;
using System.Threading.Tasks;

namespace SupermarketManagement.Data
{
    /// <summary>
    /// Gestionnaire de connexion à la base de données MySQL
    /// </summary>
    public class DatabaseConnection : IDisposable
    {
        private MySqlConnection _connection;
        private bool _disposed = false;

        /// <summary>
        /// Constructeur par défaut utilisant la chaîne de connexion de configuration
        /// </summary>
        public DatabaseConnection()
        {
            _connection = new MySqlConnection(DatabaseConfig.ConnectionString);
        }

        /// <summary>
        /// Constructeur avec chaîne de connexion personnalisée
        /// </summary>
        /// <param name="connectionString">Chaîne de connexion</param>
        public DatabaseConnection(string connectionString)
        {
            _connection = new MySqlConnection(connectionString);
        }

        /// <summary>
        /// Obtient la connexion MySQL
        /// </summary>
        public MySqlConnection Connection => _connection;

        /// <summary>
        /// État de la connexion
        /// </summary>
        public ConnectionState State => _connection?.State ?? ConnectionState.Closed;

        /// <summary>
        /// Indique si la connexion est ouverte
        /// </summary>
        public bool IsOpen => State == ConnectionState.Open;

        /// <summary>
        /// Ouvre la connexion à la base de données
        /// </summary>
        public void Open()
        {
            try
            {
                if (State == ConnectionState.Closed)
                {
                    _connection.Open();
                }
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors de l'ouverture de la connexion : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Ouvre la connexion de manière asynchrone
        /// </summary>
        public async Task OpenAsync()
        {
            try
            {
                if (State == ConnectionState.Closed)
                {
                    await _connection.OpenAsync();
                }
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors de l'ouverture asynchrone de la connexion : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Ferme la connexion à la base de données
        /// </summary>
        public void Close()
        {
            try
            {
                if (State != ConnectionState.Closed)
                {
                    _connection.Close();
                }
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors de la fermeture de la connexion : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Commence une transaction
        /// </summary>
        /// <returns>Transaction MySQL</returns>
        public MySqlTransaction BeginTransaction()
        {
            try
            {
                if (State == ConnectionState.Closed)
                    Open();
                
                return _connection.BeginTransaction();
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors du début de la transaction : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Commence une transaction avec un niveau d'isolation spécifique
        /// </summary>
        /// <param name="isolationLevel">Niveau d'isolation</param>
        /// <returns>Transaction MySQL</returns>
        public MySqlTransaction BeginTransaction(IsolationLevel isolationLevel)
        {
            try
            {
                if (State == ConnectionState.Closed)
                    Open();
                
                return _connection.BeginTransaction(isolationLevel);
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors du début de la transaction : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        /// <returns>True si la connexion réussit</returns>
        public bool TestConnection()
        {
            try
            {
                var originalState = State;
                
                if (originalState == ConnectionState.Closed)
                    Open();
                
                // Exécuter une requête simple pour tester
                using var command = new MySqlCommand("SELECT 1", _connection);
                command.ExecuteScalar();
                
                if (originalState == ConnectionState.Closed)
                    Close();
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Teste la connexion de manière asynchrone
        /// </summary>
        /// <returns>True si la connexion réussit</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var originalState = State;
                
                if (originalState == ConnectionState.Closed)
                    await OpenAsync();
                
                // Exécuter une requête simple pour tester
                using var command = new MySqlCommand("SELECT 1", _connection);
                await command.ExecuteScalarAsync();
                
                if (originalState == ConnectionState.Closed)
                    Close();
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Libère les ressources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Libère les ressources
        /// </summary>
        /// <param name="disposing">Indique si on libère les ressources managées</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _connection?.Close();
                    _connection?.Dispose();
                }
                _disposed = true;
            }
        }

        /// <summary>
        /// Destructeur
        /// </summary>
        ~DatabaseConnection()
        {
            Dispose(false);
        }
    }

    /// <summary>
    /// Factory pour créer des connexions à la base de données
    /// </summary>
    public static class DatabaseConnectionFactory
    {
        /// <summary>
        /// Crée une nouvelle connexion avec la configuration par défaut
        /// </summary>
        /// <returns>Nouvelle connexion</returns>
        public static DatabaseConnection Create()
        {
            return new DatabaseConnection();
        }

        /// <summary>
        /// Crée une nouvelle connexion avec une chaîne de connexion personnalisée
        /// </summary>
        /// <param name="connectionString">Chaîne de connexion</param>
        /// <returns>Nouvelle connexion</returns>
        public static DatabaseConnection Create(string connectionString)
        {
            return new DatabaseConnection(connectionString);
        }

        /// <summary>
        /// Crée une nouvelle connexion et l'ouvre
        /// </summary>
        /// <returns>Connexion ouverte</returns>
        public static DatabaseConnection CreateAndOpen()
        {
            var connection = new DatabaseConnection();
            connection.Open();
            return connection;
        }

        /// <summary>
        /// Crée une nouvelle connexion et l'ouvre de manière asynchrone
        /// </summary>
        /// <returns>Connexion ouverte</returns>
        public static async Task<DatabaseConnection> CreateAndOpenAsync()
        {
            var connection = new DatabaseConnection();
            await connection.OpenAsync();
            return connection;
        }
    }

    /// <summary>
    /// Exception personnalisée pour les erreurs de base de données
    /// </summary>
    public class DatabaseException : Exception
    {
        public DatabaseException(string message) : base(message) { }
        public DatabaseException(string message, Exception innerException) : base(message, innerException) { }
    }
}
