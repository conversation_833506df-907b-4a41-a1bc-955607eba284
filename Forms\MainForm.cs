using System;
using System.Drawing;
using System.Windows.Forms;
using SupermarketManagement.Forms.Products;
using SupermarketManagement.Forms.Clients;
using SupermarketManagement.Models;
using SupermarketManagement.Services;
using SupermarketManagement.Configuration;

namespace SupermarketManagement.Forms
{
    /// <summary>
    /// Formulaire principal de l'application
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly Utilisateur _utilisateurConnecte;
        private readonly IServiceProvider _serviceProvider;
        private MenuStrip menuStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel lblUtilisateur, lblDate, lblHeure;
        private Timer timerHeure;

        public MainForm(Utilisateur utilisateur, IServiceProvider serviceProvider)
        {
            _utilisateurConnecte = utilisateur ?? throw new ArgumentNullException(nameof(utilisateur));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configuration du formulaire principal
            this.Text = AppConfig.ApplicationName + " - " + AppConfig.Version;
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.IsMdiContainer = true;
            this.BackColor = Color.FromArgb(240, 240, 240);

            CreateMenuStrip();
            CreateStatusStrip();
            CreateTimer();

            this.ResumeLayout(false);
        }

        private void InitializeForm()
        {
            this.Load += MainForm_Load;
            this.FormClosing += MainForm_FormClosing;
        }

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip
            {
                BackColor = Color.FromArgb(51, 122, 183),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10)
            };

            // Menu Fichier
            var menuFichier = new ToolStripMenuItem("&Fichier");
            menuFichier.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("&Nouveau", null, null, Keys.Control | Keys.N),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Sauvegarder", null, MenuSauvegarder_Click, Keys.Control | Keys.S),
                new ToolStripMenuItem("&Restaurer", null, MenuRestaurer_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Quitter", null, MenuQuitter_Click, Keys.Alt | Keys.F4)
            });

            // Menu Gestion
            var menuGestion = new ToolStripMenuItem("&Gestion");
            
            var menuProduits = new ToolStripMenuItem("&Produits");
            menuProduits.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Liste des produits", null, MenuProduitsList_Click),
                new ToolStripMenuItem("Ajouter un produit", null, MenuProduitsAdd_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Catégories", null, MenuCategories_Click),
                new ToolStripMenuItem("Unités de mesure", null, MenuUnites_Click)
            });

            var menuClients = new ToolStripMenuItem("&Clients");
            menuClients.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Liste des clients", null, MenuClientsList_Click),
                new ToolStripMenuItem("Ajouter un client", null, MenuClientsAdd_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Comptes clients", null, MenuComptesClients_Click)
            });

            var menuFournisseurs = new ToolStripMenuItem("&Fournisseurs");
            menuFournisseurs.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Liste des fournisseurs", null, MenuFournisseursList_Click),
                new ToolStripMenuItem("Ajouter un fournisseur", null, MenuFournisseursAdd_Click)
            });

            menuGestion.DropDownItems.AddRange(new ToolStripItem[]
            {
                menuProduits,
                menuClients,
                menuFournisseurs
            });

            // Menu Ventes
            var menuVentes = new ToolStripMenuItem("&Ventes");
            menuVentes.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Nouvelle vente", null, MenuNouvelleVente_Click, Keys.F2),
                new ToolStripMenuItem("Liste des factures", null, MenuFacturesVente_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Retours clients", null, MenuRetoursClients_Click),
                new ToolStripMenuItem("Promotions", null, MenuPromotions_Click)
            });

            // Menu Achats
            var menuAchats = new ToolStripMenuItem("&Achats");
            menuAchats.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Nouvel achat", null, MenuNouvelAchat_Click),
                new ToolStripMenuItem("Liste des factures", null, MenuFacturesAchat_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Retours fournisseurs", null, MenuRetoursFournisseurs_Click)
            });

            // Menu Stock
            var menuStock = new ToolStripMenuItem("&Stock");
            menuStock.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("État du stock", null, MenuEtatStock_Click),
                new ToolStripMenuItem("Mouvements de stock", null, MenuMouvementsStock_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Inventaire", null, MenuInventaire_Click),
                new ToolStripMenuItem("Ajustements", null, MenuAjustements_Click)
            });

            // Menu Finances
            var menuFinances = new ToolStripMenuItem("&Finances");
            menuFinances.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Caisses", null, MenuCaisses_Click),
                new ToolStripMenuItem("Banques", null, MenuBanques_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Dépenses", null, MenuDepenses_Click),
                new ToolStripMenuItem("Recettes", null, MenuRecettes_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Paiements clients", null, MenuPaiementsClients_Click),
                new ToolStripMenuItem("Paiements fournisseurs", null, MenuPaiementsFournisseurs_Click)
            });

            // Menu Rapports
            var menuRapports = new ToolStripMenuItem("&Rapports");
            menuRapports.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Tableau de bord", null, MenuTableauBord_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Rapport des ventes", null, MenuRapportVentes_Click),
                new ToolStripMenuItem("Rapport des achats", null, MenuRapportAchats_Click),
                new ToolStripMenuItem("Rapport de stock", null, MenuRapportStock_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Rapport financier", null, MenuRapportFinancier_Click)
            });

            // Menu Outils
            var menuOutils = new ToolStripMenuItem("&Outils");
            menuOutils.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Paramètres", null, MenuParametres_Click),
                new ToolStripMenuItem("Utilisateurs", null, MenuUtilisateurs_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Calculatrice", null, MenuCalculatrice_Click),
                new ToolStripMenuItem("Calendrier", null, MenuCalendrier_Click)
            });

            // Menu Aide
            var menuAide = new ToolStripMenuItem("&Aide");
            menuAide.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Manuel d'utilisation", null, MenuManuel_Click, Keys.F1),
                new ToolStripSeparator(),
                new ToolStripMenuItem("À propos", null, MenuAPropos_Click)
            });

            // Ajout des menus principaux
            menuStrip.Items.AddRange(new ToolStripItem[]
            {
                menuFichier, menuGestion, menuVentes, menuAchats, menuStock, menuFinances, menuRapports, menuOutils, menuAide
            });

            // Appliquer le style aux menus
            foreach (ToolStripMenuItem item in menuStrip.Items)
            {
                item.ForeColor = Color.White;
                item.BackColor = Color.FromArgb(51, 122, 183);
            }

            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(248, 249, 250),
                Font = new Font("Segoe UI", 9)
            };

            lblUtilisateur = new ToolStripStatusLabel
            {
                Text = $"Utilisateur: {_utilisateurConnecte.NomComplet} ({_utilisateurConnecte.Role})",
                BorderSides = ToolStripStatusLabelBorderSides.Right,
                BorderStyle = Border3DStyle.Etched
            };

            lblDate = new ToolStripStatusLabel
            {
                Text = $"Date: {DateTime.Now:dd/MM/yyyy}",
                BorderSides = ToolStripStatusLabelBorderSides.Right,
                BorderStyle = Border3DStyle.Etched
            };

            lblHeure = new ToolStripStatusLabel
            {
                Text = $"Heure: {DateTime.Now:HH:mm:ss}",
                BorderSides = ToolStripStatusLabelBorderSides.Right,
                BorderStyle = Border3DStyle.Etched
            };

            var lblVersion = new ToolStripStatusLabel
            {
                Text = $"Version: {AppConfig.Version}",
                Spring = true,
                TextAlign = ContentAlignment.MiddleRight
            };

            statusStrip.Items.AddRange(new ToolStripItem[]
            {
                lblUtilisateur, lblDate, lblHeure, lblVersion
            });

            this.Controls.Add(statusStrip);
        }

        private void CreateTimer()
        {
            timerHeure = new Timer
            {
                Interval = 1000, // 1 seconde
                Enabled = true
            };
            timerHeure.Tick += TimerHeure_Tick;
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // Afficher un message de bienvenue
            var welcomeMessage = $"Bienvenue {_utilisateurConnecte.NomComplet} !";
            MessageBox.Show(welcomeMessage, "Bienvenue", MessageBoxButtons.OK, MessageBoxIcon.Information);

            // Ouvrir le tableau de bord par défaut
            MenuTableauBord_Click(sender, e);
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("Êtes-vous sûr de vouloir quitter l'application ?", 
                "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
        }

        private void TimerHeure_Tick(object sender, EventArgs e)
        {
            lblHeure.Text = $"Heure: {DateTime.Now:HH:mm:ss}";
            
            // Mettre à jour la date si nécessaire
            var currentDate = DateTime.Now.ToString("dd/MM/yyyy");
            if (!lblDate.Text.Contains(currentDate))
            {
                lblDate.Text = $"Date: {currentDate}";
            }
        }

        #region Événements des menus

        // Menu Fichier
        private void MenuSauvegarder_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonction de sauvegarde à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuRestaurer_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonction de restauration à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuQuitter_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        // Menu Gestion - Produits
        private void MenuProduitsList_Click(object sender, EventArgs e)
        {
            // Ouvrir le formulaire de liste des produits
            // var form = new FrmProductsList(_serviceProvider.GetService<IProduitRepository>(), 
            //                                _serviceProvider.GetService<INotificationService>());
            // form.MdiParent = this;
            // form.Show();
            MessageBox.Show("Ouverture de la liste des produits", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuProduitsAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Ajout d'un nouveau produit", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuCategories_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des catégories", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuUnites_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des unités de mesure", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Menu Gestion - Clients
        private void MenuClientsList_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Ouverture de la liste des clients", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuClientsAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Ajout d'un nouveau client", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuComptesClients_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des comptes clients", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Menu Gestion - Fournisseurs
        private void MenuFournisseursList_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Ouverture de la liste des fournisseurs", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuFournisseursAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Ajout d'un nouveau fournisseur", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Menu Ventes
        private void MenuNouvelleVente_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Nouvelle vente", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuFacturesVente_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Liste des factures de vente", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuRetoursClients_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des retours clients", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuPromotions_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des promotions", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Menu Achats
        private void MenuNouvelAchat_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Nouvel achat", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuFacturesAchat_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Liste des factures d'achat", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuRetoursFournisseurs_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des retours fournisseurs", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Menu Stock
        private void MenuEtatStock_Click(object sender, EventArgs e)
        {
            MessageBox.Show("État du stock", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuMouvementsStock_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Mouvements de stock", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuInventaire_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des inventaires", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuAjustements_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Ajustements de stock", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Menu Finances
        private void MenuCaisses_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des caisses", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuBanques_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des banques", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuDepenses_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des dépenses", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuRecettes_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des recettes", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuPaiementsClients_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Paiements clients", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuPaiementsFournisseurs_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Paiements fournisseurs", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Menu Rapports
        private void MenuTableauBord_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Tableau de bord", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuRapportVentes_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Rapport des ventes", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuRapportAchats_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Rapport des achats", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuRapportStock_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Rapport de stock", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuRapportFinancier_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Rapport financier", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Menu Outils
        private void MenuParametres_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Paramètres de l'application", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuUtilisateurs_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion des utilisateurs", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuCalculatrice_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Process.Start("calc.exe");
        }

        private void MenuCalendrier_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Calendrier", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Menu Aide
        private void MenuManuel_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Manuel d'utilisation", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void MenuAPropos_Click(object sender, EventArgs e)
        {
            var message = $"{AppConfig.ApplicationName}\n" +
                         $"Version: {AppConfig.Version}\n" +
                         $"Développé pour la gestion de supermarché\n" +
                         $"Algérie - {DateTime.Now.Year}";
            
            MessageBox.Show(message, "À propos", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion
    }
}
