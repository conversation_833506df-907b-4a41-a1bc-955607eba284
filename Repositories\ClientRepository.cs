using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using SupermarketManagement.Data;
using SupermarketManagement.Models;

namespace SupermarketManagement.Repositories
{
    /// <summary>
    /// Interface pour le repository des clients
    /// </summary>
    public interface IClientRepository : IActivatableRepository<Client>
    {
        /// <summary>
        /// Obtient un client par son code
        /// </summary>
        /// <param name="codeClient">Code du client</param>
        /// <returns>Client ou null</returns>
        Task<Client> GetByCodeAsync(string codeClient);

        /// <summary>
        /// Vérifie si un code client existe déjà
        /// </summary>
        /// <param name="codeClient">Code à vérifier</param>
        /// <param name="excludeId">ID à exclure de la vérification</param>
        /// <returns>True si le code existe</returns>
        Task<bool> CodeExistsAsync(string codeClient, int? excludeId = null);

        /// <summary>
        /// Recherche des clients par nom, prénom ou raison sociale
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des clients correspondants</returns>
        Task<IEnumerable<Client>> SearchByNameAsync(string searchTerm);

        /// <summary>
        /// Obtient les clients avec un solde non nul
        /// </summary>
        /// <returns>Liste des clients avec solde</returns>
        Task<IEnumerable<Client>> GetClientsWithBalanceAsync();

        /// <summary>
        /// Met à jour le solde d'un client
        /// </summary>
        /// <param name="clientId">ID du client</param>
        /// <param name="montant">Montant à ajouter (peut être négatif)</param>
        /// <returns>True si la mise à jour réussit</returns>
        Task<bool> UpdateSoldeAsync(int clientId, decimal montant);

        /// <summary>
        /// Obtient le solde actuel d'un client
        /// </summary>
        /// <param name="clientId">ID du client</param>
        /// <returns>Solde actuel</returns>
        Task<decimal> GetSoldeAsync(int clientId);

        /// <summary>
        /// Génère le prochain code client automatique
        /// </summary>
        /// <returns>Nouveau code client</returns>
        Task<string> GenerateNextCodeAsync();
    }

    /// <summary>
    /// Repository pour la gestion des clients
    /// </summary>
    public class ClientRepository : ActivatableRepository<Client>, IClientRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        public ClientRepository(DapperContext context) : base(context, "clients")
        {
        }

        /// <summary>
        /// Obtient un client par son code
        /// </summary>
        public async Task<Client> GetByCodeAsync(string codeClient)
        {
            var sql = "SELECT * FROM clients WHERE code_client = @CodeClient";
            return await _context.QueryFirstOrDefaultAsync<Client>(sql, new { CodeClient = codeClient });
        }

        /// <summary>
        /// Vérifie si un code client existe déjà
        /// </summary>
        public async Task<bool> CodeExistsAsync(string codeClient, int? excludeId = null)
        {
            var sql = "SELECT COUNT(1) FROM clients WHERE code_client = @CodeClient";
            var parameters = new { CodeClient = codeClient };

            if (excludeId.HasValue)
            {
                sql += " AND id != @ExcludeId";
                parameters = new { CodeClient = codeClient, ExcludeId = excludeId.Value };
            }

            var count = await _context.ExecuteScalarAsync<int>(sql, parameters);
            return count > 0;
        }

        /// <summary>
        /// Recherche des clients par nom, prénom ou raison sociale
        /// </summary>
        public async Task<IEnumerable<Client>> SearchByNameAsync(string searchTerm)
        {
            var sql = @"
                SELECT * FROM clients 
                WHERE actif = 1 
                AND (nom LIKE @SearchTerm 
                     OR prenom LIKE @SearchTerm 
                     OR raison_sociale LIKE @SearchTerm
                     OR code_client LIKE @SearchTerm)
                ORDER BY nom, prenom";

            return await _context.QueryAsync<Client>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        /// <summary>
        /// Obtient les clients avec un solde non nul
        /// </summary>
        public async Task<IEnumerable<Client>> GetClientsWithBalanceAsync()
        {
            var sql = @"
                SELECT * FROM clients 
                WHERE actif = 1 AND solde_actuel != 0 
                ORDER BY solde_actuel DESC";

            return await _context.QueryAsync<Client>(sql);
        }

        /// <summary>
        /// Met à jour le solde d'un client
        /// </summary>
        public async Task<bool> UpdateSoldeAsync(int clientId, decimal montant)
        {
            var sql = @"
                UPDATE clients 
                SET solde_actuel = solde_actuel + @Montant,
                    date_modification = @DateModification
                WHERE id = @ClientId";

            var affectedRows = await _context.ExecuteAsync(sql, new 
            { 
                ClientId = clientId, 
                Montant = montant,
                DateModification = DateTime.Now
            });

            return affectedRows > 0;
        }

        /// <summary>
        /// Obtient le solde actuel d'un client
        /// </summary>
        public async Task<decimal> GetSoldeAsync(int clientId)
        {
            var sql = "SELECT solde_actuel FROM clients WHERE id = @ClientId";
            return await _context.ExecuteScalarAsync<decimal>(sql, new { ClientId = clientId });
        }

        /// <summary>
        /// Génère le prochain code client automatique
        /// </summary>
        public async Task<string> GenerateNextCodeAsync()
        {
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTRING(code_client, 3) AS UNSIGNED)), 0) + 1 
                FROM clients 
                WHERE code_client REGEXP '^CL[0-9]+$'";

            var nextNumber = await _context.ExecuteScalarAsync<int>(sql);
            return $"CL{nextNumber:D6}"; // Format: CL000001
        }

        /// <summary>
        /// Génère le SQL d'insertion
        /// </summary>
        protected override string GenerateInsertSql()
        {
            return @"
                INSERT INTO clients (
                    code_client, nom, prenom, raison_sociale, type_client, adresse, ville, wilaya, 
                    code_postal, telephone, email, nif, nis, rc, art, solde_initial, solde_actuel, 
                    limite_credit, actif, date_creation, date_modification
                ) VALUES (
                    @Code, @Nom, @Prenom, @RaisonSociale, @TypeClient, @Adresse, @Ville, @Wilaya,
                    @CodePostal, @Telephone, @Email, @NIF, @NIS, @RC, @ART, @SoldeInitial, @SoldeActuel,
                    @LimiteCredit, @Actif, @DateCreation, @DateModification
                );
                SELECT LAST_INSERT_ID();";
        }

        /// <summary>
        /// Génère le SQL de mise à jour
        /// </summary>
        protected override string GenerateUpdateSql()
        {
            return @"
                UPDATE clients SET 
                    code_client = @Code,
                    nom = @Nom,
                    prenom = @Prenom,
                    raison_sociale = @RaisonSociale,
                    type_client = @TypeClient,
                    adresse = @Adresse,
                    ville = @Ville,
                    wilaya = @Wilaya,
                    code_postal = @CodePostal,
                    telephone = @Telephone,
                    email = @Email,
                    nif = @NIF,
                    nis = @NIS,
                    rc = @RC,
                    art = @ART,
                    solde_initial = @SoldeInitial,
                    solde_actuel = @SoldeActuel,
                    limite_credit = @LimiteCredit,
                    actif = @Actif,
                    date_modification = @DateModification
                WHERE id = @Id";
        }

        /// <summary>
        /// Obtient les colonnes de recherche
        /// </summary>
        protected override string[] GetSearchColumns()
        {
            return new[] { "code_client", "nom", "prenom", "raison_sociale", "telephone", "email" };
        }
    }
}
