-- =====================================================
-- Base de données pour Système de Gestion Supermarché
-- Pays: Algérie | Langue: Français
-- SGBD: MySQL | Framework: .NET WinForms + Dapper
-- =====================================================

CREATE DATABASE IF NOT EXISTS `supermarket_db` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `supermarket_db`;

-- =====================================================
-- 1. TABLES DE CONFIGURATION SYSTÈME
-- =====================================================

-- Table des paramètres de l'entreprise
CREATE TABLE `parametres_entreprise` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom_entreprise` VARCHAR(255) NOT NULL,
    `adresse` TEXT,
    `telephone` VARCHAR(50),
    `email` VARCHAR(100),
    `nif` VARCHAR(50),
    `nis` VARCHAR(50),
    `rc` VARCHAR(50),
    `art` VARCHAR(50),
    `logo_path` VARCHAR(500),
    `taux_tva_defaut` DECIMAL(5,2) DEFAULT 19.00,
    `devise` VARCHAR(10) DEFAULT 'DZD',
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `date_modification` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des utilisateurs et permissions
CREATE TABLE `utilisateurs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom_utilisateur` VARCHAR(100) UNIQUE NOT NULL,
    `mot_de_passe` VARCHAR(255) NOT NULL,
    `nom_complet` VARCHAR(255) NOT NULL,
    `email` VARCHAR(100),
    `telephone` VARCHAR(50),
    `role` ENUM('admin', 'vendeur', 'gestionnaire', 'comptable') NOT NULL,
    `permissions` JSON,
    `actif` BOOLEAN DEFAULT TRUE,
    `derniere_connexion` TIMESTAMP NULL,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `date_modification` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des sessions utilisateurs
CREATE TABLE `sessions_utilisateurs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `utilisateur_id` INT NOT NULL,
    `date_connexion` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `date_deconnexion` TIMESTAMP NULL,
    `adresse_ip` VARCHAR(45),
    `statut` ENUM('active', 'fermee') DEFAULT 'active',
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- =====================================================
-- 2. GESTION DES CLIENTS ET FOURNISSEURS
-- =====================================================

-- Table des clients
CREATE TABLE `clients` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `code_client` VARCHAR(50) UNIQUE NOT NULL,
    `nom` VARCHAR(255) NOT NULL,
    `prenom` VARCHAR(255),
    `raison_sociale` VARCHAR(255),
    `type_client` ENUM('particulier', 'entreprise') DEFAULT 'particulier',
    `adresse` TEXT,
    `ville` VARCHAR(100),
    `wilaya` VARCHAR(100),
    `code_postal` VARCHAR(20),
    `telephone` VARCHAR(50),
    `email` VARCHAR(100),
    `nif` VARCHAR(50),
    `nis` VARCHAR(50),
    `rc` VARCHAR(50),
    `art` VARCHAR(50),
    `solde_initial` DECIMAL(15,2) DEFAULT 0.00,
    `solde_actuel` DECIMAL(15,2) DEFAULT 0.00,
    `limite_credit` DECIMAL(15,2) DEFAULT 0.00,
    `actif` BOOLEAN DEFAULT TRUE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `date_modification` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des fournisseurs
CREATE TABLE `fournisseurs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `code_fournisseur` VARCHAR(50) UNIQUE NOT NULL,
    `nom` VARCHAR(255) NOT NULL,
    `raison_sociale` VARCHAR(255),
    `adresse` TEXT,
    `ville` VARCHAR(100),
    `wilaya` VARCHAR(100),
    `code_postal` VARCHAR(20),
    `telephone` VARCHAR(50),
    `email` VARCHAR(100),
    `nif` VARCHAR(50) NOT NULL,
    `nis` VARCHAR(50),
    `rc` VARCHAR(50),
    `art` VARCHAR(50),
    `solde_initial` DECIMAL(15,2) DEFAULT 0.00,
    `solde_actuel` DECIMAL(15,2) DEFAULT 0.00,
    `delai_paiement` INT DEFAULT 30,
    `actif` BOOLEAN DEFAULT TRUE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `date_modification` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. GESTION DES PRODUITS ET STOCK
-- =====================================================

-- Table des catégories de produits
CREATE TABLE `categories` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `parent_id` INT NULL,
    `actif` BOOLEAN DEFAULT TRUE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`parent_id`) REFERENCES `categories`(`id`)
);

-- Table des unités de mesure
CREATE TABLE `unites_mesure` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom` VARCHAR(100) NOT NULL,
    `symbole` VARCHAR(20) NOT NULL,
    `type` ENUM('poids', 'volume', 'longueur', 'piece') NOT NULL,
    `actif` BOOLEAN DEFAULT TRUE
);

-- Table des produits
CREATE TABLE `produits` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `code_produit` VARCHAR(100) UNIQUE NOT NULL,
    `nom` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `categorie_id` INT NOT NULL,
    `unite_base_id` INT NOT NULL,
    `prix_achat_unitaire` DECIMAL(15,2) NOT NULL,
    `prix_vente_unitaire` DECIMAL(15,2) NOT NULL,
    `prix_vente_ht` DECIMAL(15,2) NOT NULL,
    `taux_tva` DECIMAL(5,2) DEFAULT 19.00,
    `prix_fardeau` DECIMAL(15,2),
    `pieces_par_fardeau` INT DEFAULT 1,
    `stock_min` DECIMAL(10,2) DEFAULT 0,
    `stock_max` DECIMAL(10,2) DEFAULT 0,
    `stock_actuel` DECIMAL(10,2) DEFAULT 0,
    `stock_reserve` DECIMAL(10,2) DEFAULT 0,
    `pmp` DECIMAL(15,2) DEFAULT 0.00, -- Prix Moyen Pondéré
    `image_path` VARCHAR(500),
    `actif` BOOLEAN DEFAULT TRUE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `date_modification` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`categorie_id`) REFERENCES `categories`(`id`),
    FOREIGN KEY (`unite_base_id`) REFERENCES `unites_mesure`(`id`)
);

-- Table des codes-barres multiples pour un produit
CREATE TABLE `codes_barres` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `produit_id` INT NOT NULL,
    `code_barre` VARCHAR(255) NOT NULL UNIQUE,
    `unite_id` INT NOT NULL,
    `quantite_unite` DECIMAL(10,2) DEFAULT 1.00,
    `principal` BOOLEAN DEFAULT FALSE,
    `actif` BOOLEAN DEFAULT TRUE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`),
    FOREIGN KEY (`unite_id`) REFERENCES `unites_mesure`(`id`)
);

-- Table des unités de vente multiples
CREATE TABLE `unites_vente_produit` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `produit_id` INT NOT NULL,
    `unite_id` INT NOT NULL,
    `facteur_conversion` DECIMAL(10,4) NOT NULL DEFAULT 1.0000,
    `prix_vente` DECIMAL(15,2) NOT NULL,
    `code_barre` VARCHAR(255),
    `actif` BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`),
    FOREIGN KEY (`unite_id`) REFERENCES `unites_mesure`(`id`)
);

-- =====================================================
-- 4. GESTION FINANCIÈRE - CAISSES ET BANQUES
-- =====================================================

-- Table des caisses
CREATE TABLE `caisses` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `solde_initial` DECIMAL(15,2) DEFAULT 0.00,
    `solde_actuel` DECIMAL(15,2) DEFAULT 0.00,
    `devise` VARCHAR(10) DEFAULT 'DZD',
    `actif` BOOLEAN DEFAULT TRUE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des banques
CREATE TABLE `banques` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom_banque` VARCHAR(255) NOT NULL,
    `numero_compte` VARCHAR(100) NOT NULL,
    `rib` VARCHAR(50),
    `swift` VARCHAR(20),
    `solde_initial` DECIMAL(15,2) DEFAULT 0.00,
    `solde_actuel` DECIMAL(15,2) DEFAULT 0.00,
    `devise` VARCHAR(10) DEFAULT 'DZD',
    `actif` BOOLEAN DEFAULT TRUE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 5. GESTION DES VENTES
-- =====================================================

-- Table des factures de vente
CREATE TABLE `factures_vente` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_facture` VARCHAR(100) UNIQUE NOT NULL,
    `client_id` INT NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_facture` DATE NOT NULL,
    `heure_facture` TIME NOT NULL,
    `type_facture` ENUM('vente', 'proforma', 'bon_livraison') DEFAULT 'vente',
    `sous_total_ht` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `total_remise` DECIMAL(15,2) DEFAULT 0.00,
    `total_tva` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `total_ttc` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `mode_paiement` ENUM('especes', 'cheque', 'virement', 'carte', 'credit', 'mixte') DEFAULT 'especes',
    `montant_paye` DECIMAL(15,2) DEFAULT 0.00,
    `montant_rendu` DECIMAL(15,2) DEFAULT 0.00,
    `statut` ENUM('brouillon', 'validee', 'payee', 'annulee') DEFAULT 'brouillon',
    `notes` TEXT,
    `caisse_id` INT,
    `banque_id` INT,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `date_modification` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`),
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`),
    FOREIGN KEY (`banque_id`) REFERENCES `banques`(`id`)
);

-- Table des détails de factures de vente
CREATE TABLE `details_facture_vente` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `facture_id` INT NOT NULL,
    `produit_id` INT NOT NULL,
    `unite_id` INT NOT NULL,
    `quantite` DECIMAL(10,2) NOT NULL,
    `prix_unitaire_ht` DECIMAL(15,2) NOT NULL,
    `prix_unitaire_ttc` DECIMAL(15,2) NOT NULL,
    `remise_pourcentage` DECIMAL(5,2) DEFAULT 0.00,
    `remise_montant` DECIMAL(15,2) DEFAULT 0.00,
    `taux_tva` DECIMAL(5,2) NOT NULL,
    `montant_tva` DECIMAL(15,2) NOT NULL,
    `sous_total_ht` DECIMAL(15,2) NOT NULL,
    `sous_total_ttc` DECIMAL(15,2) NOT NULL,
    FOREIGN KEY (`facture_id`) REFERENCES `factures_vente`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`),
    FOREIGN KEY (`unite_id`) REFERENCES `unites_mesure`(`id`)
);

-- Table des retours clients
CREATE TABLE `retours_client` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_retour` VARCHAR(100) UNIQUE NOT NULL,
    `facture_vente_id` INT,
    `client_id` INT NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_retour` DATE NOT NULL,
    `motif` TEXT,
    `total_retour` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `statut` ENUM('en_attente', 'validee', 'annulee') DEFAULT 'en_attente',
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`facture_vente_id`) REFERENCES `factures_vente`(`id`),
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- Table des détails de retours clients
CREATE TABLE `details_retour_client` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `retour_id` INT NOT NULL,
    `produit_id` INT NOT NULL,
    `unite_id` INT NOT NULL,
    `quantite_retournee` DECIMAL(10,2) NOT NULL,
    `prix_unitaire` DECIMAL(15,2) NOT NULL,
    `sous_total` DECIMAL(15,2) NOT NULL,
    FOREIGN KEY (`retour_id`) REFERENCES `retours_client`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`),
    FOREIGN KEY (`unite_id`) REFERENCES `unites_mesure`(`id`)
);

-- =====================================================
-- 6. GESTION DES ACHATS
-- =====================================================

-- Table des factures d'achat
CREATE TABLE `factures_achat` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_facture` VARCHAR(100) UNIQUE NOT NULL,
    `numero_facture_fournisseur` VARCHAR(100),
    `fournisseur_id` INT NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_facture` DATE NOT NULL,
    `date_echeance` DATE,
    `sous_total_ht` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `total_remise` DECIMAL(15,2) DEFAULT 0.00,
    `total_tva` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `total_ttc` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `montant_paye` DECIMAL(15,2) DEFAULT 0.00,
    `statut` ENUM('brouillon', 'validee', 'payee', 'annulee') DEFAULT 'brouillon',
    `mode_paiement` ENUM('especes', 'cheque', 'virement', 'credit') DEFAULT 'credit',
    `notes` TEXT,
    `caisse_id` INT,
    `banque_id` INT,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `date_modification` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`fournisseur_id`) REFERENCES `fournisseurs`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`),
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`),
    FOREIGN KEY (`banque_id`) REFERENCES `banques`(`id`)
);

-- Table des détails de factures d'achat
CREATE TABLE `details_facture_achat` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `facture_id` INT NOT NULL,
    `produit_id` INT NOT NULL,
    `unite_id` INT NOT NULL,
    `quantite` DECIMAL(10,2) NOT NULL,
    `prix_unitaire_ht` DECIMAL(15,2) NOT NULL,
    `prix_unitaire_ttc` DECIMAL(15,2) NOT NULL,
    `remise_pourcentage` DECIMAL(5,2) DEFAULT 0.00,
    `remise_montant` DECIMAL(15,2) DEFAULT 0.00,
    `taux_tva` DECIMAL(5,2) NOT NULL,
    `montant_tva` DECIMAL(15,2) NOT NULL,
    `sous_total_ht` DECIMAL(15,2) NOT NULL,
    `sous_total_ttc` DECIMAL(15,2) NOT NULL,
    FOREIGN KEY (`facture_id`) REFERENCES `factures_achat`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`),
    FOREIGN KEY (`unite_id`) REFERENCES `unites_mesure`(`id`)
);

-- Table des retours fournisseurs
CREATE TABLE `retours_fournisseur` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_retour` VARCHAR(100) UNIQUE NOT NULL,
    `facture_achat_id` INT,
    `fournisseur_id` INT NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_retour` DATE NOT NULL,
    `motif` TEXT,
    `total_retour` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `statut` ENUM('en_attente', 'validee', 'annulee') DEFAULT 'en_attente',
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`facture_achat_id`) REFERENCES `factures_achat`(`id`),
    FOREIGN KEY (`fournisseur_id`) REFERENCES `fournisseurs`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- Table des détails de retours fournisseurs
CREATE TABLE `details_retour_fournisseur` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `retour_id` INT NOT NULL,
    `produit_id` INT NOT NULL,
    `unite_id` INT NOT NULL,
    `quantite_retournee` DECIMAL(10,2) NOT NULL,
    `prix_unitaire` DECIMAL(15,2) NOT NULL,
    `sous_total` DECIMAL(15,2) NOT NULL,
    FOREIGN KEY (`retour_id`) REFERENCES `retours_fournisseur`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`),
    FOREIGN KEY (`unite_id`) REFERENCES `unites_mesure`(`id`)
);

-- =====================================================
-- 7. GESTION DES PROMOTIONS ET PACKS
-- =====================================================

-- Table des promotions
CREATE TABLE `promotions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `type_promotion` ENUM('pourcentage', 'montant_fixe', 'pack', 'achetez_obtenez') NOT NULL,
    `valeur_remise` DECIMAL(15,2),
    `pourcentage_remise` DECIMAL(5,2),
    `date_debut` DATE NOT NULL,
    `date_fin` DATE NOT NULL,
    `quantite_min` DECIMAL(10,2) DEFAULT 1,
    `actif` BOOLEAN DEFAULT TRUE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des produits en promotion
CREATE TABLE `produits_promotion` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `promotion_id` INT NOT NULL,
    `produit_id` INT NOT NULL,
    `prix_promo` DECIMAL(15,2),
    FOREIGN KEY (`promotion_id`) REFERENCES `promotions`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`)
);

-- Table des packs de produits
CREATE TABLE `packs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `prix_pack` DECIMAL(15,2) NOT NULL,
    `actif` BOOLEAN DEFAULT TRUE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des détails des packs
CREATE TABLE `details_pack` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `pack_id` INT NOT NULL,
    `produit_id` INT NOT NULL,
    `quantite` DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (`pack_id`) REFERENCES `packs`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`)
);

-- =====================================================
-- 8. GESTION DES MOUVEMENTS DE STOCK
-- =====================================================

-- Table des mouvements de stock
CREATE TABLE `mouvements_stock` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `produit_id` INT NOT NULL,
    `type_mouvement` ENUM('entree', 'sortie', 'ajustement', 'transfert', 'inventaire') NOT NULL,
    `reference_document` VARCHAR(100),
    `quantite` DECIMAL(10,2) NOT NULL,
    `prix_unitaire` DECIMAL(15,2),
    `stock_avant` DECIMAL(10,2) NOT NULL,
    `stock_apres` DECIMAL(10,2) NOT NULL,
    `motif` TEXT,
    `utilisateur_id` INT NOT NULL,
    `date_mouvement` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- Table des inventaires
CREATE TABLE `inventaires` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_inventaire` VARCHAR(100) UNIQUE NOT NULL,
    `date_inventaire` DATE NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `statut` ENUM('en_cours', 'termine', 'valide') DEFAULT 'en_cours',
    `notes` TEXT,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- Table des détails d'inventaire
CREATE TABLE `details_inventaire` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `inventaire_id` INT NOT NULL,
    `produit_id` INT NOT NULL,
    `stock_theorique` DECIMAL(10,2) NOT NULL,
    `stock_physique` DECIMAL(10,2) NOT NULL,
    `ecart` DECIMAL(10,2) NOT NULL,
    `valeur_ecart` DECIMAL(15,2) NOT NULL,
    FOREIGN KEY (`inventaire_id`) REFERENCES `inventaires`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`)
);

-- =====================================================
-- 9. COMPTABILITÉ GÉNÉRALE
-- =====================================================

-- Table des comptes comptables
CREATE TABLE `comptes_comptables` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_compte` VARCHAR(20) UNIQUE NOT NULL,
    `nom_compte` VARCHAR(255) NOT NULL,
    `type_compte` ENUM('actif', 'passif', 'charge', 'produit', 'resultat') NOT NULL,
    `classe` ENUM('1', '2', '3', '4', '5', '6', '7') NOT NULL,
    `parent_id` INT NULL,
    `solde_debiteur` DECIMAL(15,2) DEFAULT 0.00,
    `solde_crediteur` DECIMAL(15,2) DEFAULT 0.00,
    `actif` BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (`parent_id`) REFERENCES `comptes_comptables`(`id`)
);

-- Table des écritures comptables
CREATE TABLE `ecritures_comptables` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_ecriture` VARCHAR(100) UNIQUE NOT NULL,
    `date_ecriture` DATE NOT NULL,
    `libelle` VARCHAR(255) NOT NULL,
    `reference_document` VARCHAR(100),
    `type_document` ENUM('facture_vente', 'facture_achat', 'paiement', 'encaissement', 'autre') NOT NULL,
    `document_id` INT,
    `utilisateur_id` INT NOT NULL,
    `validee` BOOLEAN DEFAULT FALSE,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- Table des lignes d'écritures comptables
CREATE TABLE `lignes_ecriture` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `ecriture_id` INT NOT NULL,
    `compte_id` INT NOT NULL,
    `libelle` VARCHAR(255),
    `debit` DECIMAL(15,2) DEFAULT 0.00,
    `credit` DECIMAL(15,2) DEFAULT 0.00,
    FOREIGN KEY (`ecriture_id`) REFERENCES `ecritures_comptables`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`compte_id`) REFERENCES `comptes_comptables`(`id`)
);

-- =====================================================
-- 10. GESTION DES DÉPENSES ET RECETTES
-- =====================================================

-- Table des catégories de dépenses
CREATE TABLE `categories_depenses` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `compte_comptable_id` INT,
    `actif` BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (`compte_comptable_id`) REFERENCES `comptes_comptables`(`id`)
);

-- Table des dépenses
CREATE TABLE `depenses` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_depense` VARCHAR(100) UNIQUE NOT NULL,
    `categorie_id` INT NOT NULL,
    `fournisseur_id` INT,
    `libelle` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `montant` DECIMAL(15,2) NOT NULL,
    `date_depense` DATE NOT NULL,
    `mode_paiement` ENUM('especes', 'cheque', 'virement', 'carte') NOT NULL,
    `numero_piece` VARCHAR(100),
    `caisse_id` INT,
    `banque_id` INT,
    `utilisateur_id` INT NOT NULL,
    `statut` ENUM('brouillon', 'validee', 'annulee') DEFAULT 'brouillon',
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`categorie_id`) REFERENCES `categories_depenses`(`id`),
    FOREIGN KEY (`fournisseur_id`) REFERENCES `fournisseurs`(`id`),
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`),
    FOREIGN KEY (`banque_id`) REFERENCES `banques`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- Table des catégories de recettes
CREATE TABLE `categories_recettes` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `compte_comptable_id` INT,
    `actif` BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (`compte_comptable_id`) REFERENCES `comptes_comptables`(`id`)
);

-- Table des recettes
CREATE TABLE `recettes` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_recette` VARCHAR(100) UNIQUE NOT NULL,
    `categorie_id` INT NOT NULL,
    `client_id` INT,
    `libelle` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `montant` DECIMAL(15,2) NOT NULL,
    `date_recette` DATE NOT NULL,
    `mode_paiement` ENUM('especes', 'cheque', 'virement', 'carte') NOT NULL,
    `numero_piece` VARCHAR(100),
    `caisse_id` INT,
    `banque_id` INT,
    `utilisateur_id` INT NOT NULL,
    `statut` ENUM('brouillon', 'validee', 'annulee') DEFAULT 'brouillon',
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`categorie_id`) REFERENCES `categories_recettes`(`id`),
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`),
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`),
    FOREIGN KEY (`banque_id`) REFERENCES `banques`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- =====================================================
-- 11. GESTION DES PAIEMENTS
-- =====================================================

-- Table des paiements clients
CREATE TABLE `paiements_clients` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_paiement` VARCHAR(100) UNIQUE NOT NULL,
    `client_id` INT NOT NULL,
    `facture_id` INT,
    `montant` DECIMAL(15,2) NOT NULL,
    `mode_paiement` ENUM('especes', 'cheque', 'virement', 'carte') NOT NULL,
    `numero_piece` VARCHAR(100),
    `date_paiement` DATE NOT NULL,
    `caisse_id` INT,
    `banque_id` INT,
    `utilisateur_id` INT NOT NULL,
    `notes` TEXT,
    `statut` ENUM('en_attente', 'encaisse', 'annule') DEFAULT 'encaisse',
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`),
    FOREIGN KEY (`facture_id`) REFERENCES `factures_vente`(`id`),
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`),
    FOREIGN KEY (`banque_id`) REFERENCES `banques`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- Table des paiements fournisseurs
CREATE TABLE `paiements_fournisseurs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `numero_paiement` VARCHAR(100) UNIQUE NOT NULL,
    `fournisseur_id` INT NOT NULL,
    `facture_id` INT,
    `montant` DECIMAL(15,2) NOT NULL,
    `mode_paiement` ENUM('especes', 'cheque', 'virement', 'carte') NOT NULL,
    `numero_piece` VARCHAR(100),
    `date_paiement` DATE NOT NULL,
    `caisse_id` INT,
    `banque_id` INT,
    `utilisateur_id` INT NOT NULL,
    `notes` TEXT,
    `statut` ENUM('en_attente', 'paye', 'annule') DEFAULT 'paye',
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`fournisseur_id`) REFERENCES `fournisseurs`(`id`),
    FOREIGN KEY (`facture_id`) REFERENCES `factures_achat`(`id`),
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`),
    FOREIGN KEY (`banque_id`) REFERENCES `banques`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- =====================================================
-- 12. MOUVEMENTS DE CAISSE ET BANQUE
-- =====================================================

-- Table des mouvements de caisse
CREATE TABLE `mouvements_caisse` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `caisse_id` INT NOT NULL,
    `type_mouvement` ENUM('entree', 'sortie') NOT NULL,
    `montant` DECIMAL(15,2) NOT NULL,
    `libelle` VARCHAR(255) NOT NULL,
    `reference_document` VARCHAR(100),
    `type_document` ENUM('vente', 'achat', 'depense', 'recette', 'paiement_client', 'paiement_fournisseur', 'autre') NOT NULL,
    `document_id` INT,
    `solde_avant` DECIMAL(15,2) NOT NULL,
    `solde_apres` DECIMAL(15,2) NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_mouvement` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- Table des mouvements de banque
CREATE TABLE `mouvements_banque` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `banque_id` INT NOT NULL,
    `type_mouvement` ENUM('entree', 'sortie') NOT NULL,
    `montant` DECIMAL(15,2) NOT NULL,
    `libelle` VARCHAR(255) NOT NULL,
    `reference_document` VARCHAR(100),
    `type_document` ENUM('vente', 'achat', 'depense', 'recette', 'paiement_client', 'paiement_fournisseur', 'autre') NOT NULL,
    `document_id` INT,
    `solde_avant` DECIMAL(15,2) NOT NULL,
    `solde_apres` DECIMAL(15,2) NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_mouvement` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`banque_id`) REFERENCES `banques`(`id`),
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- =====================================================
-- 13. SAUVEGARDE ET LOGS
-- =====================================================

-- Table des sauvegardes
CREATE TABLE `sauvegardes` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `nom_fichier` VARCHAR(255) NOT NULL,
    `chemin_fichier` VARCHAR(500) NOT NULL,
    `taille_fichier` BIGINT,
    `type_sauvegarde` ENUM('manuelle', 'automatique') DEFAULT 'manuelle',
    `utilisateur_id` INT,
    `date_creation` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- Table des logs système
CREATE TABLE `logs_systeme` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `utilisateur_id` INT,
    `action` VARCHAR(255) NOT NULL,
    `table_affectee` VARCHAR(100),
    `id_enregistrement` INT,
    `anciennes_valeurs` JSON,
    `nouvelles_valeurs` JSON,
    `adresse_ip` VARCHAR(45),
    `date_action` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`)
);

-- =====================================================
-- 14. INDEX POUR OPTIMISATION DES PERFORMANCES
-- =====================================================

-- Index sur les tables principales
CREATE INDEX idx_clients_code ON clients(code_client);
CREATE INDEX idx_clients_nom ON clients(nom);
CREATE INDEX idx_fournisseurs_code ON fournisseurs(code_fournisseur);
CREATE INDEX idx_fournisseurs_nom ON fournisseurs(nom);
CREATE INDEX idx_produits_code ON produits(code_produit);
CREATE INDEX idx_produits_nom ON produits(nom);
CREATE INDEX idx_codes_barres_code ON codes_barres(code_barre);
CREATE INDEX idx_factures_vente_numero ON factures_vente(numero_facture);
CREATE INDEX idx_factures_vente_date ON factures_vente(date_facture);
CREATE INDEX idx_factures_achat_numero ON factures_achat(numero_facture);
CREATE INDEX idx_factures_achat_date ON factures_achat(date_facture);
CREATE INDEX idx_mouvements_stock_date ON mouvements_stock(date_mouvement);
CREATE INDEX idx_mouvements_stock_produit ON mouvements_stock(produit_id);
CREATE INDEX idx_ecritures_date ON ecritures_comptables(date_ecriture);
CREATE INDEX idx_paiements_clients_date ON paiements_clients(date_paiement);
CREATE INDEX idx_paiements_fournisseurs_date ON paiements_fournisseurs(date_paiement);

-- =====================================================
-- 15. VUES POUR RAPPORTS ET TABLEAUX DE BORD
-- =====================================================

-- Vue pour le tableau de bord des ventes
CREATE VIEW vue_tableau_bord_ventes AS
SELECT
    DATE(fv.date_facture) as date_vente,
    COUNT(fv.id) as nombre_factures,
    SUM(fv.total_ttc) as chiffre_affaires,
    SUM(fv.total_ttc - fv.total_tva) as total_ht,
    SUM(fv.total_tva) as total_tva,
    AVG(fv.total_ttc) as panier_moyen
FROM factures_vente fv
WHERE fv.statut = 'validee'
GROUP BY DATE(fv.date_facture);

-- Vue pour l'état du stock
CREATE VIEW vue_etat_stock AS
SELECT
    p.id,
    p.code_produit,
    p.nom,
    c.nom as categorie,
    p.stock_actuel,
    p.stock_min,
    p.stock_max,
    p.stock_reserve,
    p.prix_achat_unitaire,
    p.prix_vente_unitaire,
    (p.stock_actuel * p.prix_achat_unitaire) as valeur_stock,
    CASE
        WHEN p.stock_actuel <= p.stock_min THEN 'Rupture'
        WHEN p.stock_actuel <= (p.stock_min * 1.2) THEN 'Alerte'
        ELSE 'Normal'
    END as statut_stock
FROM produits p
LEFT JOIN categories c ON p.categorie_id = c.id
WHERE p.actif = TRUE;

-- Vue pour les comptes clients
CREATE VIEW vue_comptes_clients AS
SELECT
    c.id,
    c.code_client,
    c.nom,
    c.prenom,
    c.solde_actuel,
    COALESCE(SUM(CASE WHEN fv.statut = 'validee' THEN fv.total_ttc ELSE 0 END), 0) as total_factures,
    COALESCE(SUM(CASE WHEN pc.statut = 'encaisse' THEN pc.montant ELSE 0 END), 0) as total_paiements,
    (c.solde_actuel + COALESCE(SUM(CASE WHEN fv.statut = 'validee' THEN fv.total_ttc ELSE 0 END), 0) -
     COALESCE(SUM(CASE WHEN pc.statut = 'encaisse' THEN pc.montant ELSE 0 END), 0)) as solde_final
FROM clients c
LEFT JOIN factures_vente fv ON c.id = fv.client_id
LEFT JOIN paiements_clients pc ON c.id = pc.client_id
WHERE c.actif = TRUE
GROUP BY c.id, c.code_client, c.nom, c.prenom, c.solde_actuel;

-- Vue pour les comptes fournisseurs
CREATE VIEW vue_comptes_fournisseurs AS
SELECT
    f.id,
    f.code_fournisseur,
    f.nom,
    f.solde_actuel,
    COALESCE(SUM(CASE WHEN fa.statut = 'validee' THEN fa.total_ttc ELSE 0 END), 0) as total_factures,
    COALESCE(SUM(CASE WHEN pf.statut = 'paye' THEN pf.montant ELSE 0 END), 0) as total_paiements,
    (f.solde_actuel + COALESCE(SUM(CASE WHEN fa.statut = 'validee' THEN fa.total_ttc ELSE 0 END), 0) -
     COALESCE(SUM(CASE WHEN pf.statut = 'paye' THEN pf.montant ELSE 0 END), 0)) as solde_final
FROM fournisseurs f
LEFT JOIN factures_achat fa ON f.id = fa.fournisseur_id
LEFT JOIN paiements_fournisseurs pf ON f.id = pf.fournisseur_id
WHERE f.actif = TRUE
GROUP BY f.id, f.code_fournisseur, f.nom, f.solde_actuel;

-- =====================================================
-- 16. DONNÉES INITIALES
-- =====================================================

-- Insertion des paramètres de l'entreprise par défaut
INSERT INTO `parametres_entreprise` (
    `nom_entreprise`, `adresse`, `telephone`, `email`,
    `nif`, `nis`, `rc`, `art`, `taux_tva_defaut`, `devise`
) VALUES (
    'Mon Supermarché',
    'Adresse de l\'entreprise, Alger, Algérie',
    '+213 XX XX XX XX',
    '<EMAIL>',
    '000000000000000',
    '000000000000000',
    '00/00-0000000',
    '00000000000000000',
    19.00,
    'DZD'
);

-- Insertion de l'utilisateur administrateur par défaut
INSERT INTO `utilisateurs` (
    `nom_utilisateur`, `mot_de_passe`, `nom_complet`,
    `email`, `role`, `permissions`, `actif`
) VALUES (
    'admin',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'Administrateur Système',
    '<EMAIL>',
    'admin',
    '{"all": true}',
    TRUE
);

-- Insertion des unités de mesure de base
INSERT INTO `unites_mesure` (`nom`, `symbole`, `type`, `actif`) VALUES
('Pièce', 'Pcs', 'piece', TRUE),
('Kilogramme', 'Kg', 'poids', TRUE),
('Gramme', 'g', 'poids', TRUE),
('Litre', 'L', 'volume', TRUE),
('Millilitre', 'ml', 'volume', TRUE),
('Mètre', 'm', 'longueur', TRUE),
('Centimètre', 'cm', 'longueur', TRUE),
('Carton', 'Ctn', 'piece', TRUE),
('Boîte', 'Bte', 'piece', TRUE),
('Paquet', 'Pqt', 'piece', TRUE);

-- Insertion des catégories de base
INSERT INTO `categories` (`nom`, `description`, `parent_id`, `actif`) VALUES
('Alimentation', 'Produits alimentaires', NULL, TRUE),
('Boissons', 'Boissons diverses', NULL, TRUE),
('Hygiène', 'Produits d\'hygiène', NULL, TRUE),
('Entretien', 'Produits d\'entretien', NULL, TRUE),
('Électronique', 'Appareils électroniques', NULL, TRUE);

-- Sous-catégories pour Alimentation
INSERT INTO `categories` (`nom`, `description`, `parent_id`, `actif`) VALUES
('Fruits et Légumes', 'Fruits et légumes frais', 1, TRUE),
('Viandes', 'Viandes et charcuteries', 1, TRUE),
('Produits laitiers', 'Lait, fromages, yaourts', 1, TRUE),
('Épicerie', 'Produits d\'épicerie sèche', 1, TRUE);

-- Insertion d'une caisse par défaut
INSERT INTO `caisses` (`nom`, `description`, `solde_initial`, `solde_actuel`, `devise`, `actif`) VALUES
('Caisse Principale', 'Caisse principale du magasin', 0.00, 0.00, 'DZD', TRUE);

-- Insertion des comptes comptables de base (Plan comptable algérien)
INSERT INTO `comptes_comptables` (`numero_compte`, `nom_compte`, `type_compte`, `classe`) VALUES
('10', 'Capital', 'passif', '1'),
('11', 'Réserves', 'passif', '1'),
('12', 'Résultat de l\'exercice', 'passif', '1'),
('20', 'Immobilisations incorporelles', 'actif', '2'),
('21', 'Immobilisations corporelles', 'actif', '2'),
('30', 'Stocks de marchandises', 'actif', '3'),
('31', 'Matières premières', 'actif', '3'),
('40', 'Fournisseurs', 'passif', '4'),
('41', 'Clients', 'actif', '4'),
('42', 'Personnel', 'passif', '4'),
('44', 'État', 'actif', '4'),
('50', 'Valeurs mobilières de placement', 'actif', '5'),
('51', 'Banques', 'actif', '5'),
('53', 'Caisse', 'actif', '5'),
('60', 'Achats', 'charge', '6'),
('61', 'Services extérieurs', 'charge', '6'),
('62', 'Autres services extérieurs', 'charge', '6'),
('63', 'Charges de personnel', 'charge', '6'),
('70', 'Ventes', 'produit', '7'),
('71', 'Production stockée', 'produit', '7'),
('75', 'Autres produits de gestion courante', 'produit', '7');

-- Insertion des catégories de dépenses par défaut
INSERT INTO `categories_depenses` (`nom`, `description`) VALUES
('Électricité', 'Factures d\'électricité'),
('Eau', 'Factures d\'eau'),
('Téléphone', 'Factures de téléphone et internet'),
('Loyer', 'Loyer du local commercial'),
('Assurance', 'Assurances diverses'),
('Entretien', 'Frais d\'entretien et réparation'),
('Transport', 'Frais de transport et carburant'),
('Publicité', 'Frais de publicité et marketing'),
('Fournitures', 'Fournitures de bureau'),
('Autres', 'Autres dépenses diverses');

-- Insertion des catégories de recettes par défaut
INSERT INTO `categories_recettes` (`nom`, `description`) VALUES
('Ventes', 'Recettes des ventes'),
('Intérêts', 'Intérêts bancaires'),
('Subventions', 'Subventions reçues'),
('Autres', 'Autres recettes diverses');

-- =====================================================
-- 17. PROCÉDURES STOCKÉES UTILES
-- =====================================================

DELIMITER //

-- Procédure pour mettre à jour le stock après une vente
CREATE PROCEDURE UpdateStockAfterSale(
    IN p_produit_id INT,
    IN p_quantite DECIMAL(10,2),
    IN p_prix_unitaire DECIMAL(15,2),
    IN p_utilisateur_id INT,
    IN p_reference VARCHAR(100)
)
BEGIN
    DECLARE v_stock_avant DECIMAL(10,2);
    DECLARE v_stock_apres DECIMAL(10,2);

    -- Récupérer le stock actuel
    SELECT stock_actuel INTO v_stock_avant
    FROM produits
    WHERE id = p_produit_id;

    -- Calculer le nouveau stock
    SET v_stock_apres = v_stock_avant - p_quantite;

    -- Mettre à jour le stock du produit
    UPDATE produits
    SET stock_actuel = v_stock_apres,
        date_modification = CURRENT_TIMESTAMP
    WHERE id = p_produit_id;

    -- Enregistrer le mouvement de stock
    INSERT INTO mouvements_stock (
        produit_id, type_mouvement, reference_document, quantite,
        prix_unitaire, stock_avant, stock_apres, motif, utilisateur_id
    ) VALUES (
        p_produit_id, 'sortie', p_reference, p_quantite,
        p_prix_unitaire, v_stock_avant, v_stock_apres, 'Vente', p_utilisateur_id
    );
END //

-- Procédure pour mettre à jour le stock après un achat
CREATE PROCEDURE UpdateStockAfterPurchase(
    IN p_produit_id INT,
    IN p_quantite DECIMAL(10,2),
    IN p_prix_unitaire DECIMAL(15,2),
    IN p_utilisateur_id INT,
    IN p_reference VARCHAR(100)
)
BEGIN
    DECLARE v_stock_avant DECIMAL(10,2);
    DECLARE v_stock_apres DECIMAL(10,2);
    DECLARE v_ancien_pmp DECIMAL(15,2);
    DECLARE v_nouveau_pmp DECIMAL(15,2);

    -- Récupérer le stock actuel et le PMP
    SELECT stock_actuel, pmp INTO v_stock_avant, v_ancien_pmp
    FROM produits
    WHERE id = p_produit_id;

    -- Calculer le nouveau stock
    SET v_stock_apres = v_stock_avant + p_quantite;

    -- Calculer le nouveau PMP
    IF v_stock_avant > 0 THEN
        SET v_nouveau_pmp = ((v_stock_avant * v_ancien_pmp) + (p_quantite * p_prix_unitaire)) / v_stock_apres;
    ELSE
        SET v_nouveau_pmp = p_prix_unitaire;
    END IF;

    -- Mettre à jour le stock et le PMP du produit
    UPDATE produits
    SET stock_actuel = v_stock_apres,
        pmp = v_nouveau_pmp,
        date_modification = CURRENT_TIMESTAMP
    WHERE id = p_produit_id;

    -- Enregistrer le mouvement de stock
    INSERT INTO mouvements_stock (
        produit_id, type_mouvement, reference_document, quantite,
        prix_unitaire, stock_avant, stock_apres, motif, utilisateur_id
    ) VALUES (
        p_produit_id, 'entree', p_reference, p_quantite,
        p_prix_unitaire, v_stock_avant, v_stock_apres, 'Achat', p_utilisateur_id
    );
END //

-- Procédure pour calculer le chiffre d'affaires d'une période
CREATE PROCEDURE GetChiffreAffaires(
    IN p_date_debut DATE,
    IN p_date_fin DATE
)
BEGIN
    SELECT
        COUNT(id) as nombre_factures,
        SUM(total_ttc) as chiffre_affaires_ttc,
        SUM(sous_total_ht) as chiffre_affaires_ht,
        SUM(total_tva) as total_tva,
        AVG(total_ttc) as panier_moyen
    FROM factures_vente
    WHERE date_facture BETWEEN p_date_debut AND p_date_fin
    AND statut = 'validee';
END //

-- Procédure pour générer un numéro de facture automatique
CREATE PROCEDURE GenerateInvoiceNumber(
    IN p_type VARCHAR(20),
    OUT p_numero VARCHAR(100)
)
BEGIN
    DECLARE v_count INT;
    DECLARE v_year VARCHAR(4);
    DECLARE v_prefix VARCHAR(10);

    SET v_year = YEAR(CURDATE());

    CASE p_type
        WHEN 'vente' THEN SET v_prefix = 'FV';
        WHEN 'achat' THEN SET v_prefix = 'FA';
        WHEN 'proforma' THEN SET v_prefix = 'PF';
        WHEN 'bon_livraison' THEN SET v_prefix = 'BL';
        ELSE SET v_prefix = 'DOC';
    END CASE;

    -- Compter les factures de ce type pour cette année
    IF p_type = 'vente' THEN
        SELECT COUNT(*) + 1 INTO v_count
        FROM factures_vente
        WHERE YEAR(date_facture) = v_year;
    ELSEIF p_type = 'achat' THEN
        SELECT COUNT(*) + 1 INTO v_count
        FROM factures_achat
        WHERE YEAR(date_facture) = v_year;
    ELSE
        SET v_count = 1;
    END IF;

    SET p_numero = CONCAT(v_prefix, v_year, LPAD(v_count, 6, '0'));
END //

DELIMITER ;

-- =====================================================
-- 18. TRIGGERS POUR AUTOMATISATION
-- =====================================================

-- Trigger pour mettre à jour le solde client après une facture
DELIMITER //
CREATE TRIGGER after_facture_vente_insert
AFTER INSERT ON factures_vente
FOR EACH ROW
BEGIN
    IF NEW.statut = 'validee' THEN
        UPDATE clients
        SET solde_actuel = solde_actuel + NEW.total_ttc
        WHERE id = NEW.client_id;
    END IF;
END //
DELIMITER ;

-- Trigger pour mettre à jour le solde fournisseur après une facture d'achat
DELIMITER //
CREATE TRIGGER after_facture_achat_insert
AFTER INSERT ON factures_achat
FOR EACH ROW
BEGIN
    IF NEW.statut = 'validee' THEN
        UPDATE fournisseurs
        SET solde_actuel = solde_actuel + NEW.total_ttc
        WHERE id = NEW.fournisseur_id;
    END IF;
END //
DELIMITER ;

-- =====================================================
-- 19. COMMENTAIRES ET DOCUMENTATION
-- =====================================================

/*
GUIDE D'UTILISATION DE LA BASE DE DONNÉES

1. CONFIGURATION INITIALE :
   - Modifier les paramètres dans 'parametres_entreprise'
   - Créer les utilisateurs dans 'utilisateurs'
   - Configurer les caisses et banques

2. GESTION DES PRODUITS :
   - Créer les catégories dans 'categories'
   - Ajouter les produits dans 'produits'
   - Configurer les codes-barres multiples dans 'codes_barres'
   - Définir les unités de vente dans 'unites_vente_produit'

3. GESTION DES VENTES :
   - Créer les factures dans 'factures_vente'
   - Ajouter les détails dans 'details_facture_vente'
   - Utiliser les procédures stockées pour mettre à jour le stock

4. RAPPORTS DISPONIBLES :
   - Vue 'vue_tableau_bord_ventes' pour le tableau de bord
   - Vue 'vue_etat_stock' pour l'état du stock
   - Vue 'vue_comptes_clients' pour les comptes clients
   - Vue 'vue_comptes_fournisseurs' pour les comptes fournisseurs

5. SÉCURITÉ :
   - Tous les mots de passe doivent être hashés
   - Utiliser les logs système pour tracer les actions
   - Effectuer des sauvegardes régulières

NOTES IMPORTANTES :
- La TVA par défaut est de 19% (modifiable dans parametres_entreprise)
- Les prix sont stockés en DZD (Dinar Algérien)
- Le système supporte les codes-barres multiples par produit
- Les stocks sont gérés automatiquement via les triggers
*/

-- =====================================================
-- FIN DU SCRIPT - BASE DE DONNÉES PRÊTE À L'UTILISATION
-- =====================================================
