using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;
using MySql.Data.MySqlClient;
using SupermarketManagement.Configuration;

namespace SupermarketManagement.Data
{
    /// <summary>
    /// Contexte Dapper pour l'accès aux données
    /// </summary>
    public class DapperContext : IDisposable
    {
        private readonly DatabaseConnection _connection;
        private bool _disposed = false;

        /// <summary>
        /// Constructeur
        /// </summary>
        public DapperContext()
        {
            _connection = new DatabaseConnection();
            
            // Configuration de Dapper pour MySQL
            ConfigureDapper();
        }

        /// <summary>
        /// Constructeur avec chaîne de connexion personnalisée
        /// </summary>
        /// <param name="connectionString">Chaîne de connexion</param>
        public DapperContext(string connectionString)
        {
            _connection = new DatabaseConnection(connectionString);
            ConfigureDapper();
        }

        /// <summary>
        /// Obtient la connexion de base de données
        /// </summary>
        public IDbConnection Connection => _connection.Connection;

        /// <summary>
        /// Configure Dapper pour MySQL
        /// </summary>
        private void ConfigureDapper()
        {
            // Configuration des mappings de types pour MySQL
            SqlMapper.AddTypeHandler(new DateTimeHandler());
            SqlMapper.AddTypeHandler(new TimeSpanHandler());
            SqlMapper.AddTypeHandler(new BooleanHandler());
            
            // Configuration du timeout par défaut
            SqlMapper.Settings.CommandTimeout = DatabaseConfig.CommandTimeout;
        }

        /// <summary>
        /// Exécute une requête et retourne les résultats
        /// </summary>
        /// <typeparam name="T">Type de retour</typeparam>
        /// <param name="sql">Requête SQL</param>
        /// <param name="param">Paramètres</param>
        /// <param name="transaction">Transaction</param>
        /// <param name="commandTimeout">Timeout de commande</param>
        /// <returns>Liste des résultats</returns>
        public async Task<IEnumerable<T>> QueryAsync<T>(string sql, object param = null, 
            IDbTransaction transaction = null, int? commandTimeout = null)
        {
            try
            {
                await EnsureConnectionOpenAsync();
                return await Connection.QueryAsync<T>(sql, param, transaction, commandTimeout);
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors de l'exécution de la requête : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Exécute une requête et retourne le premier résultat ou null
        /// </summary>
        /// <typeparam name="T">Type de retour</typeparam>
        /// <param name="sql">Requête SQL</param>
        /// <param name="param">Paramètres</param>
        /// <param name="transaction">Transaction</param>
        /// <param name="commandTimeout">Timeout de commande</param>
        /// <returns>Premier résultat ou null</returns>
        public async Task<T> QueryFirstOrDefaultAsync<T>(string sql, object param = null, 
            IDbTransaction transaction = null, int? commandTimeout = null)
        {
            try
            {
                await EnsureConnectionOpenAsync();
                return await Connection.QueryFirstOrDefaultAsync<T>(sql, param, transaction, commandTimeout);
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors de l'exécution de la requête : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Exécute une requête et retourne un seul résultat
        /// </summary>
        /// <typeparam name="T">Type de retour</typeparam>
        /// <param name="sql">Requête SQL</param>
        /// <param name="param">Paramètres</param>
        /// <param name="transaction">Transaction</param>
        /// <param name="commandTimeout">Timeout de commande</param>
        /// <returns>Résultat unique</returns>
        public async Task<T> QuerySingleAsync<T>(string sql, object param = null, 
            IDbTransaction transaction = null, int? commandTimeout = null)
        {
            try
            {
                await EnsureConnectionOpenAsync();
                return await Connection.QuerySingleAsync<T>(sql, param, transaction, commandTimeout);
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors de l'exécution de la requête : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Exécute une commande et retourne le nombre de lignes affectées
        /// </summary>
        /// <param name="sql">Commande SQL</param>
        /// <param name="param">Paramètres</param>
        /// <param name="transaction">Transaction</param>
        /// <param name="commandTimeout">Timeout de commande</param>
        /// <returns>Nombre de lignes affectées</returns>
        public async Task<int> ExecuteAsync(string sql, object param = null, 
            IDbTransaction transaction = null, int? commandTimeout = null)
        {
            try
            {
                await EnsureConnectionOpenAsync();
                return await Connection.ExecuteAsync(sql, param, transaction, commandTimeout);
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors de l'exécution de la commande : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Exécute une commande et retourne une valeur scalaire
        /// </summary>
        /// <typeparam name="T">Type de retour</typeparam>
        /// <param name="sql">Commande SQL</param>
        /// <param name="param">Paramètres</param>
        /// <param name="transaction">Transaction</param>
        /// <param name="commandTimeout">Timeout de commande</param>
        /// <returns>Valeur scalaire</returns>
        public async Task<T> ExecuteScalarAsync<T>(string sql, object param = null, 
            IDbTransaction transaction = null, int? commandTimeout = null)
        {
            try
            {
                await EnsureConnectionOpenAsync();
                return await Connection.ExecuteScalarAsync<T>(sql, param, transaction, commandTimeout);
            }
            catch (MySqlException ex)
            {
                throw new DatabaseException($"Erreur lors de l'exécution de la commande scalaire : {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Commence une transaction
        /// </summary>
        /// <returns>Transaction</returns>
        public async Task<IDbTransaction> BeginTransactionAsync()
        {
            await EnsureConnectionOpenAsync();
            return _connection.BeginTransaction();
        }

        /// <summary>
        /// Commence une transaction avec un niveau d'isolation spécifique
        /// </summary>
        /// <param name="isolationLevel">Niveau d'isolation</param>
        /// <returns>Transaction</returns>
        public async Task<IDbTransaction> BeginTransactionAsync(IsolationLevel isolationLevel)
        {
            await EnsureConnectionOpenAsync();
            return _connection.BeginTransaction(isolationLevel);
        }

        /// <summary>
        /// S'assure que la connexion est ouverte
        /// </summary>
        private async Task EnsureConnectionOpenAsync()
        {
            if (!_connection.IsOpen)
            {
                await _connection.OpenAsync();
            }
        }

        /// <summary>
        /// Libère les ressources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Libère les ressources
        /// </summary>
        /// <param name="disposing">Indique si on libère les ressources managées</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _connection?.Dispose();
                }
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Gestionnaire de type DateTime pour Dapper/MySQL
    /// </summary>
    public class DateTimeHandler : SqlMapper.TypeHandler<DateTime>
    {
        public override void SetValue(IDbDataParameter parameter, DateTime value)
        {
            parameter.Value = value;
        }

        public override DateTime Parse(object value)
        {
            return DateTime.SpecifyKind((DateTime)value, DateTimeKind.Local);
        }
    }

    /// <summary>
    /// Gestionnaire de type TimeSpan pour Dapper/MySQL
    /// </summary>
    public class TimeSpanHandler : SqlMapper.TypeHandler<TimeSpan>
    {
        public override void SetValue(IDbDataParameter parameter, TimeSpan value)
        {
            parameter.Value = value;
        }

        public override TimeSpan Parse(object value)
        {
            if (value is TimeSpan timeSpan)
                return timeSpan;
            
            if (value is string stringValue && TimeSpan.TryParse(stringValue, out var parsedTimeSpan))
                return parsedTimeSpan;
            
            return TimeSpan.Zero;
        }
    }

    /// <summary>
    /// Gestionnaire de type Boolean pour Dapper/MySQL
    /// </summary>
    public class BooleanHandler : SqlMapper.TypeHandler<bool>
    {
        public override void SetValue(IDbDataParameter parameter, bool value)
        {
            parameter.Value = value ? 1 : 0;
        }

        public override bool Parse(object value)
        {
            if (value is bool boolValue)
                return boolValue;
            
            if (value is sbyte sbyteValue)
                return sbyteValue != 0;
            
            if (value is byte byteValue)
                return byteValue != 0;
            
            if (value is int intValue)
                return intValue != 0;
            
            return false;
        }
    }
}
