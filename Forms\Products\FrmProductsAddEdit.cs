using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SupermarketManagement.Models;
using SupermarketManagement.Repositories;
using SupermarketManagement.Services;

namespace SupermarketManagement.Forms.Products
{
    /// <summary>
    /// Formulaire d'ajout/modification de produit
    /// </summary>
    public partial class FrmProductsAddEdit : Form
    {
        private readonly IProduitRepository _produitRepository;
        private readonly INotificationService _notificationService;
        private readonly Produit _produit;
        private readonly bool _isEditMode;

        // Contrôles du formulaire
        private TextBox txtCode, txtNom, txtDescription;
        private ComboBox cmbCategorie, cmbUniteBase;
        private NumericUpDown nudPrixAchat, nudPrixVente, nudTauxTVA;
        private NumericUpDown nudStockMin, nudStockMax, nudStockActuel;
        private NumericUpDown nudPrixFardeau, nudPiecesParFardeau;
        private CheckBox chkActif;
        private Button btnSave, btnCancel, btnGenerateCode;
        private PictureBox picImage;
        private Button btnSelectImage, btnRemoveImage;
        private Label lblPrixVenteHT, lblMarge, lblPourcentageMarge;

        public FrmProductsAddEdit(IProduitRepository produitRepository, INotificationService notificationService, Produit produit = null)
        {
            _produitRepository = produitRepository ?? throw new ArgumentNullException(nameof(produitRepository));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _produit = produit;
            _isEditMode = produit != null;

            InitializeComponent();
            InitializeFormControls();
            InitializeForm();
        }

        private void InitializeFormControls()
        {
            this.SuspendLayout();

            // Configuration du formulaire
            this.Text = _isEditMode ? "Modifier le Produit" : "Ajouter un Produit";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();

            this.ResumeLayout(false);
        }

        private void InitializeForm()
        {
            this.Load += FrmProductsAddEdit_Load;
        }

        private void CreateControls()
        {
            // Panel principal avec scroll
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };

            // Informations générales
            var grpGeneral = CreateGroupBox("Informations Générales", new Point(0, 0), new Size(740, 180));

            // Code produit
            var lblCode = CreateLabel("Code Produit :", new Point(20, 30));
            txtCode = CreateTextBox(new Point(120, 27), new Size(150, 25));
            btnGenerateCode = CreateButton("Générer", new Point(280, 26), new Size(80, 27), Color.FromArgb(108, 117, 125));
            btnGenerateCode.Click += BtnGenerateCode_Click;

            // Nom
            var lblNom = CreateLabel("Nom :", new Point(20, 65));
            txtNom = CreateTextBox(new Point(120, 62), new Size(300, 25));

            // Catégorie
            var lblCategorie = CreateLabel("Catégorie :", new Point(450, 30));
            cmbCategorie = CreateComboBox(new Point(530, 27), new Size(180, 25));

            // Unité de base
            var lblUniteBase = CreateLabel("Unité de base :", new Point(450, 65));
            cmbUniteBase = CreateComboBox(new Point(530, 62), new Size(180, 25));

            // Description
            var lblDescription = CreateLabel("Description :", new Point(20, 100));
            txtDescription = CreateTextBox(new Point(120, 97), new Size(590, 60), true);

            grpGeneral.Controls.AddRange(new Control[]
            {
                lblCode, txtCode, btnGenerateCode, lblNom, txtNom,
                lblCategorie, cmbCategorie, lblUniteBase, cmbUniteBase,
                lblDescription, txtDescription
            });

            // Prix et marges
            var grpPrix = CreateGroupBox("Prix et Marges", new Point(0, 190), new Size(360, 180));

            var lblPrixAchat = CreateLabel("Prix d'achat :", new Point(20, 30));
            nudPrixAchat = CreateNumericUpDown(new Point(120, 27), new Size(120, 25));
            nudPrixAchat.ValueChanged += NudPrixAchat_ValueChanged;

            var lblPrixVente = CreateLabel("Prix de vente :", new Point(20, 65));
            nudPrixVente = CreateNumericUpDown(new Point(120, 62), new Size(120, 25));
            nudPrixVente.ValueChanged += NudPrixVente_ValueChanged;

            lblPrixVenteHT = CreateLabel("Prix HT : 0,00 DA", new Point(250, 65));
            lblPrixVenteHT.ForeColor = Color.FromArgb(108, 117, 125);

            var lblTauxTVA = CreateLabel("Taux TVA (%) :", new Point(20, 100));
            nudTauxTVA = CreateNumericUpDown(new Point(120, 97), new Size(120, 25));
            nudTauxTVA.Value = 19; // Valeur par défaut pour l'Algérie
            nudTauxTVA.ValueChanged += NudTauxTVA_ValueChanged;

            lblMarge = CreateLabel("Marge : 0,00 DA", new Point(20, 135));
            lblMarge.ForeColor = Color.FromArgb(40, 167, 69);

            lblPourcentageMarge = CreateLabel("Marge (%) : 0,00%", new Point(150, 135));
            lblPourcentageMarge.ForeColor = Color.FromArgb(40, 167, 69);

            grpPrix.Controls.AddRange(new Control[]
            {
                lblPrixAchat, nudPrixAchat, lblPrixVente, nudPrixVente, lblPrixVenteHT,
                lblTauxTVA, nudTauxTVA, lblMarge, lblPourcentageMarge
            });

            // Stock
            var grpStock = CreateGroupBox("Gestion du Stock", new Point(370, 190), new Size(370, 180));

            var lblStockActuel = CreateLabel("Stock actuel :", new Point(20, 30));
            nudStockActuel = CreateNumericUpDown(new Point(120, 27), new Size(100, 25));

            var lblStockMin = CreateLabel("Stock minimum :", new Point(20, 65));
            nudStockMin = CreateNumericUpDown(new Point(120, 62), new Size(100, 25));

            var lblStockMax = CreateLabel("Stock maximum :", new Point(20, 100));
            nudStockMax = CreateNumericUpDown(new Point(120, 97), new Size(100, 25));

            var lblPrixFardeau = CreateLabel("Prix fardeau :", new Point(20, 135));
            nudPrixFardeau = CreateNumericUpDown(new Point(120, 132), new Size(100, 25));

            var lblPiecesParFardeau = CreateLabel("Pcs/Fardeau :", new Point(230, 135));
            nudPiecesParFardeau = CreateNumericUpDown(new Point(320, 132), new Size(80, 25));
            nudPiecesParFardeau.Value = 1;

            grpStock.Controls.AddRange(new Control[]
            {
                lblStockActuel, nudStockActuel, lblStockMin, nudStockMin,
                lblStockMax, nudStockMax, lblPrixFardeau, nudPrixFardeau,
                lblPiecesParFardeau, nudPiecesParFardeau
            });

            // Image
            var grpImage = CreateGroupBox("Image du Produit", new Point(0, 380), new Size(200, 150));

            picImage = new PictureBox
            {
                Location = new Point(20, 30),
                Size = new Size(80, 80),
                BorderStyle = BorderStyle.FixedSingle,
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            btnSelectImage = CreateButton("Sélectionner", new Point(110, 30), new Size(80, 30), Color.FromArgb(0, 123, 255));
            btnRemoveImage = CreateButton("Supprimer", new Point(110, 70), new Size(80, 30), Color.FromArgb(220, 53, 69));

            btnSelectImage.Click += BtnSelectImage_Click;
            btnRemoveImage.Click += BtnRemoveImage_Click;

            grpImage.Controls.AddRange(new Control[] { picImage, btnSelectImage, btnRemoveImage });

            // Statut
            var grpStatut = CreateGroupBox("Statut", new Point(210, 380), new Size(150, 80));

            chkActif = new CheckBox
            {
                Text = "Produit actif",
                Location = new Point(20, 30),
                Size = new Size(100, 25),
                Checked = true,
                Font = new Font("Segoe UI", 9)
            };

            grpStatut.Controls.Add(chkActif);

            // Boutons d'action
            var btnPanel = new Panel
            {
                Location = new Point(0, 480),
                Size = new Size(740, 50),
                BackColor = Color.FromArgb(248, 249, 250)
            };

            btnSave = CreateButton("Enregistrer", new Point(550, 10), new Size(90, 30), Color.FromArgb(40, 167, 69));
            btnCancel = CreateButton("Annuler", new Point(650, 10), new Size(90, 30), Color.FromArgb(108, 117, 125));

            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;

            btnPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });

            // Ajout des contrôles au panel principal
            mainPanel.Controls.AddRange(new Control[]
            {
                grpGeneral, grpPrix, grpStock, grpImage, grpStatut, btnPanel
            });

            this.Controls.Add(mainPanel);
        }

        private GroupBox CreateGroupBox(string text, Point location, Size size)
        {
            return new GroupBox
            {
                Text = text,
                Location = location,
                Size = size,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 122, 183)
            };
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(90, 20),
                Font = new Font("Segoe UI", 9),
                TextAlign = ContentAlignment.MiddleLeft
            };
        }

        private TextBox CreateTextBox(Point location, Size size, bool multiline = false)
        {
            return new TextBox
            {
                Location = location,
                Size = size,
                Font = new Font("Segoe UI", 9),
                Multiline = multiline,
                ScrollBars = multiline ? ScrollBars.Vertical : ScrollBars.None
            };
        }

        private ComboBox CreateComboBox(Point location, Size size)
        {
            return new ComboBox
            {
                Location = location,
                Size = size,
                Font = new Font("Segoe UI", 9),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
        }

        private NumericUpDown CreateNumericUpDown(Point location, Size size)
        {
            return new NumericUpDown
            {
                Location = location,
                Size = size,
                Font = new Font("Segoe UI", 9),
                DecimalPlaces = 2,
                Maximum = 999999999,
                Minimum = 0
            };
        }

        private Button CreateButton(string text, Point location, Size size, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = size,
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                UseVisualStyleBackColor = false
            };
            button.FlatAppearance.BorderSize = 0;
            return button;
        }

        private async void FrmProductsAddEdit_Load(object sender, EventArgs e)
        {
            await LoadComboBoxDataAsync();
            
            if (_isEditMode)
            {
                LoadProductData();
            }
            else
            {
                await GenerateCodeAsync();
            }

            CalculateMargins();
        }

        private async Task LoadComboBoxDataAsync()
        {
            try
            {
                // Charger les catégories
                // var categories = await _categorieRepository.GetActiveAsync();
                cmbCategorie.Items.Clear();
                // cmbCategorie.Items.AddRange(categories.ToArray());

                // Charger les unités de mesure
                // var unites = await _uniteRepository.GetActiveAsync();
                cmbUniteBase.Items.Clear();
                // cmbUniteBase.Items.AddRange(unites.ToArray());
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors du chargement des données : {ex.Message}");
            }
        }

        private void LoadProductData()
        {
            if (_produit == null) return;

            txtCode.Text = _produit.Code;
            txtNom.Text = _produit.Nom;
            txtDescription.Text = _produit.Description;
            nudPrixAchat.Value = _produit.PrixAchatUnitaire;
            nudPrixVente.Value = _produit.PrixVenteUnitaire;
            nudTauxTVA.Value = _produit.TauxTVA;
            nudStockActuel.Value = _produit.StockActuel;
            nudStockMin.Value = _produit.StockMin;
            nudStockMax.Value = _produit.StockMax;
            nudPrixFardeau.Value = _produit.PrixFardeau ?? 0;
            nudPiecesParFardeau.Value = _produit.PiecesParFardeau;
            chkActif.Checked = _produit.Actif;

            // Charger l'image si elle existe
            if (!string.IsNullOrEmpty(_produit.ImagePath) && System.IO.File.Exists(_produit.ImagePath))
            {
                picImage.Image = Image.FromFile(_produit.ImagePath);
            }
        }

        private async Task GenerateCodeAsync()
        {
            try
            {
                var code = await _produitRepository.GenerateNextCodeAsync();
                txtCode.Text = code;
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors de la génération du code : {ex.Message}");
            }
        }

        private async void BtnGenerateCode_Click(object sender, EventArgs e)
        {
            await GenerateCodeAsync();
        }

        private void NudPrixAchat_ValueChanged(object sender, EventArgs e)
        {
            CalculateMargins();
        }

        private void NudPrixVente_ValueChanged(object sender, EventArgs e)
        {
            CalculatePriceHT();
            CalculateMargins();
        }

        private void NudTauxTVA_ValueChanged(object sender, EventArgs e)
        {
            CalculatePriceHT();
        }

        private void CalculatePriceHT()
        {
            var calculationService = new CalculationService();
            var prixHT = calculationService.CalculerMontantHT(nudPrixVente.Value, nudTauxTVA.Value);
            lblPrixVenteHT.Text = $"Prix HT : {prixHT:N2} DA";
        }

        private void CalculateMargins()
        {
            var calculationService = new CalculationService();
            var marge = calculationService.CalculerMarge(nudPrixVente.Value, nudPrixAchat.Value);
            var pourcentageMarge = calculationService.CalculerPourcentageMarge(nudPrixVente.Value, nudPrixAchat.Value);

            lblMarge.Text = $"Marge : {marge:N2} DA";
            lblPourcentageMarge.Text = $"Marge (%) : {pourcentageMarge:N2}%";

            // Changer la couleur selon la marge
            var color = marge >= 0 ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69);
            lblMarge.ForeColor = color;
            lblPourcentageMarge.ForeColor = color;
        }

        private void BtnSelectImage_Click(object sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog
            {
                Filter = "Images|*.jpg;*.jpeg;*.png;*.bmp;*.gif",
                Title = "Sélectionner une image"
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                picImage.Image = Image.FromFile(openFileDialog.FileName);
                picImage.Tag = openFileDialog.FileName;
            }
        }

        private void BtnRemoveImage_Click(object sender, EventArgs e)
        {
            picImage.Image = null;
            picImage.Tag = null;
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                var produit = _isEditMode ? _produit : new Produit();

                // Remplir les propriétés
                produit.Code = txtCode.Text.Trim();
                produit.Nom = txtNom.Text.Trim();
                produit.Description = txtDescription.Text.Trim();
                produit.PrixAchatUnitaire = nudPrixAchat.Value;
                produit.PrixVenteUnitaire = nudPrixVente.Value;
                produit.TauxTVA = nudTauxTVA.Value;
                produit.StockActuel = nudStockActuel.Value;
                produit.StockMin = nudStockMin.Value;
                produit.StockMax = nudStockMax.Value;
                produit.PrixFardeau = nudPrixFardeau.Value > 0 ? nudPrixFardeau.Value : null;
                produit.PiecesParFardeau = (int)nudPiecesParFardeau.Value;
                produit.Actif = chkActif.Checked;

                // Calculer le prix HT
                var calculationService = new CalculationService();
                produit.PrixVenteHT = calculationService.CalculerMontantHT(produit.PrixVenteUnitaire, produit.TauxTVA);

                // Gérer l'image
                if (picImage.Tag != null)
                {
                    // Copier l'image vers le dossier des produits
                    // produit.ImagePath = await SaveProductImageAsync(picImage.Tag.ToString(), produit.Code);
                }

                if (_isEditMode)
                {
                    await _produitRepository.UpdateAsync(produit);
                    _notificationService.ShowSuccess("Succès", "Produit modifié avec succès");
                }
                else
                {
                    await _produitRepository.AddAsync(produit);
                    _notificationService.ShowSuccess("Succès", "Produit ajouté avec succès");
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors de l'enregistrement : {ex.Message}");
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                _notificationService.ShowWarning("Validation", "Le code produit est requis");
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                _notificationService.ShowWarning("Validation", "Le nom du produit est requis");
                txtNom.Focus();
                return false;
            }

            if (nudPrixAchat.Value <= 0)
            {
                _notificationService.ShowWarning("Validation", "Le prix d'achat doit être supérieur à zéro");
                nudPrixAchat.Focus();
                return false;
            }

            if (nudPrixVente.Value <= 0)
            {
                _notificationService.ShowWarning("Validation", "Le prix de vente doit être supérieur à zéro");
                nudPrixVente.Focus();
                return false;
            }

            return true;
        }
    }
}
