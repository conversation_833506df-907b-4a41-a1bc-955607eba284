using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using SupermarketManagement.Models;
using SupermarketManagement.Services;
using SupermarketManagement.Configuration;

namespace SupermarketManagement.Forms
{
    /// <summary>
    /// Formulaire de connexion
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly IAuthenticationService _authService;
        private int _tentativesConnexion = 0;
        private const int MAX_TENTATIVES = 3;

        public Utilisateur UtilisateurConnecte { get; private set; }

        /// <summary>
        /// Constructeur
        /// </summary>
        public LoginForm(IAuthenticationService authService)
        {
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// Initialise le formulaire
        /// </summary>
        private void InitializeForm()
        {
            // Configuration du formulaire
            this.Text = "Connexion - " + AppConfig.ApplicationName;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Size = new Size(400, 300);
            this.BackColor = Color.FromArgb(240, 240, 240);

            // Création des contrôles
            CreateControls();
            
            // Configuration des événements
            this.Load += LoginForm_Load;
            this.KeyPreview = true;
            this.KeyDown += LoginForm_KeyDown;
        }

        /// <summary>
        /// Crée les contrôles du formulaire
        /// </summary>
        private void CreateControls()
        {
            // Panel principal
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Logo/Titre
            var titleLabel = new Label
            {
                Text = AppConfig.ApplicationName,
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 122, 183),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 50
            };

            // Panel de connexion
            var loginPanel = new Panel
            {
                Size = new Size(300, 150),
                Location = new Point(50, 80),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Label nom d'utilisateur
            var lblUsername = new Label
            {
                Text = "Nom d'utilisateur :",
                Location = new Point(20, 20),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 9)
            };

            // TextBox nom d'utilisateur
            txtUsername = new TextBox
            {
                Location = new Point(20, 45),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 9)
            };

            // Label mot de passe
            var lblPassword = new Label
            {
                Text = "Mot de passe :",
                Location = new Point(20, 75),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 9)
            };

            // TextBox mot de passe
            txtPassword = new TextBox
            {
                Location = new Point(20, 100),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 9),
                UseSystemPasswordChar = true
            };

            // Bouton de connexion
            btnLogin = new Button
            {
                Text = "Se connecter",
                Location = new Point(20, 135),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(51, 122, 183),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9)
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;

            // Bouton d'annulation
            btnCancel = new Button
            {
                Text = "Annuler",
                Location = new Point(170, 135),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;

            // Label de statut
            lblStatus = new Label
            {
                Location = new Point(50, 240),
                Size = new Size(300, 20),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 8)
            };

            // Ajout des contrôles
            loginPanel.Controls.AddRange(new Control[] 
            { 
                lblUsername, txtUsername, lblPassword, txtPassword, btnLogin, btnCancel 
            });

            mainPanel.Controls.AddRange(new Control[] 
            { 
                titleLabel, loginPanel, lblStatus 
            });

            this.Controls.Add(mainPanel);
        }

        /// <summary>
        /// Événement de chargement du formulaire
        /// </summary>
        private async void LoginForm_Load(object sender, EventArgs e)
        {
            // Vérification de la connexion à la base de données
            if (!await VerifierConnexionBDAsync())
            {
                lblStatus.Text = "Erreur de connexion à la base de données";
                lblStatus.ForeColor = Color.Red;
                btnLogin.Enabled = false;
                return;
            }

            txtUsername.Focus();
        }

        /// <summary>
        /// Gestion des touches du clavier
        /// </summary>
        private void LoginForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                BtnLogin_Click(sender, e);
            }
            else if (e.KeyCode == Keys.Escape)
            {
                BtnCancel_Click(sender, e);
            }
        }

        /// <summary>
        /// Événement du bouton de connexion
        /// </summary>
        private async void BtnLogin_Click(object sender, EventArgs e)
        {
            await TenterConnexionAsync();
        }

        /// <summary>
        /// Événement du bouton d'annulation
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// Tente une connexion
        /// </summary>
        private async Task TenterConnexionAsync()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                AfficherMessage("Veuillez saisir votre nom d'utilisateur", Color.Red);
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                AfficherMessage("Veuillez saisir votre mot de passe", Color.Red);
                txtPassword.Focus();
                return;
            }

            // Désactivation des contrôles pendant la connexion
            SetControlsEnabled(false);
            AfficherMessage("Connexion en cours...", Color.Blue);

            try
            {
                var result = await _authService.AuthenticateAsync(txtUsername.Text, txtPassword.Text);

                if (result.Success)
                {
                    UtilisateurConnecte = result.Data;
                    AfficherMessage("Connexion réussie", Color.Green);
                    
                    // Attendre un peu pour que l'utilisateur voie le message
                    await Task.Delay(500);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    _tentativesConnexion++;
                    AfficherMessage(result.Message, Color.Red);
                    
                    if (_tentativesConnexion >= MAX_TENTATIVES)
                    {
                        AfficherMessage($"Trop de tentatives échouées. Application fermée.", Color.Red);
                        await Task.Delay(2000);
                        Application.Exit();
                        return;
                    }

                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                AfficherMessage($"Erreur de connexion : {ex.Message}", Color.Red);
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        /// <summary>
        /// Vérifie la connexion à la base de données
        /// </summary>
        private async Task<bool> VerifierConnexionBDAsync()
        {
            try
            {
                return DatabaseConfig.TestConnection();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Active/désactive les contrôles
        /// </summary>
        private void SetControlsEnabled(bool enabled)
        {
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            btnLogin.Enabled = enabled;
            btnCancel.Enabled = enabled;
        }

        /// <summary>
        /// Affiche un message de statut
        /// </summary>
        private void AfficherMessage(string message, Color couleur)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = couleur;
            Application.DoEvents();
        }

        #region Contrôles du formulaire
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Label lblStatus;
        #endregion
    }

    /// <summary>
    /// Interface pour le service d'authentification
    /// </summary>
    public interface IAuthenticationService
    {
        /// <summary>
        /// Authentifie un utilisateur
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="motDePasse">Mot de passe</param>
        /// <returns>Résultat de l'authentification</returns>
        Task<ServiceResult<Utilisateur>> AuthenticateAsync(string nomUtilisateur, string motDePasse);

        /// <summary>
        /// Déconnecte l'utilisateur actuel
        /// </summary>
        Task LogoutAsync();

        /// <summary>
        /// Obtient l'utilisateur actuellement connecté
        /// </summary>
        Utilisateur UtilisateurActuel { get; }
    }
}
