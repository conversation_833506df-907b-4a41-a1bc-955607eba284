using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Énumération des types de promotion
    /// </summary>
    public enum TypePromotion
    {
        Pourcentage,
        MontantFixe,
        Pack,
        AchetezObtenez
    }

    /// <summary>
    /// Modèle pour les promotions
    /// </summary>
    [Table("promotions")]
    public class Promotion : BaseEntity, IActivatable, INamedEntity
    {
        [Required]
        [StringLength(255)]
        [Column("nom")]
        public string Nom { get; set; }

        [Column("description")]
        public string Description { get; set; }

        [Required]
        [Column("type_promotion")]
        public TypePromotion TypePromotion { get; set; }

        [Column("valeur_remise")]
        public decimal? ValeurRemise { get; set; }

        [Column("pourcentage_remise")]
        public decimal? PourcentageRemise { get; set; }

        [Required]
        [Column("date_debut")]
        public DateTime DateDebut { get; set; }

        [Required]
        [Column("date_fin")]
        public DateTime DateFin { get; set; }

        [Column("quantite_min")]
        public decimal QuantiteMin { get; set; } = 1;

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<ProduitPromotion> ProduitsPromotion { get; set; } = new List<ProduitPromotion>();

        // Propriétés calculées
        [NotMapped]
        public bool EstActive => Actif && DateTime.Today >= DateDebut && DateTime.Today <= DateFin;

        [NotMapped]
        public bool EstExpiree => DateTime.Today > DateFin;

        [NotMapped]
        public bool EstFuture => DateTime.Today < DateDebut;

        [NotMapped]
        public int JoursRestants => EstActive ? (DateFin - DateTime.Today).Days : 0;

        [NotMapped]
        public string StatutPromotion
        {
            get
            {
                if (!Actif) return "Inactive";
                if (EstFuture) return "Future";
                if (EstActive) return "Active";
                if (EstExpiree) return "Expirée";
                return "Inconnue";
            }
        }

        [NotMapped]
        public string TypePromotionTexte
        {
            get
            {
                return TypePromotion switch
                {
                    TypePromotion.Pourcentage => "Remise en %",
                    TypePromotion.MontantFixe => "Remise fixe",
                    TypePromotion.Pack => "Pack",
                    TypePromotion.AchetezObtenez => "Achetez/Obtenez",
                    _ => "Inconnu"
                };
            }
        }
    }

    /// <summary>
    /// Modèle pour les produits en promotion
    /// </summary>
    [Table("produits_promotion")]
    public class ProduitPromotion : BaseEntity
    {
        [Required]
        [Column("promotion_id")]
        public int PromotionId { get; set; }

        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Column("prix_promo")]
        public decimal? PrixPromo { get; set; }

        // Navigation properties
        public virtual Promotion Promotion { get; set; }
        public virtual Produit Produit { get; set; }
    }

    /// <summary>
    /// Modèle pour les packs de produits
    /// </summary>
    [Table("packs")]
    public class Pack : BaseEntity, IActivatable, INamedEntity
    {
        [Required]
        [StringLength(255)]
        [Column("nom")]
        public string Nom { get; set; }

        [Column("description")]
        public string Description { get; set; }

        [Required]
        [Column("prix_pack")]
        public decimal PrixPack { get; set; }

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<DetailPack> Details { get; set; } = new List<DetailPack>();

        // Propriétés calculées
        [NotMapped]
        public decimal PrixTotalIndividuel => Details?.Sum(d => d.Produit?.PrixVenteUnitaire * d.Quantite) ?? 0;

        [NotMapped]
        public decimal EconomieRealisee => PrixTotalIndividuel - PrixPack;

        [NotMapped]
        public decimal PourcentageEconomie => PrixTotalIndividuel > 0 ? (EconomieRealisee / PrixTotalIndividuel) * 100 : 0;

        [NotMapped]
        public int NombreProduits => Details?.Count ?? 0;
    }

    /// <summary>
    /// Modèle pour les détails des packs
    /// </summary>
    [Table("details_pack")]
    public class DetailPack : BaseEntity
    {
        [Required]
        [Column("pack_id")]
        public int PackId { get; set; }

        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Required]
        [Column("quantite")]
        public decimal Quantite { get; set; }

        // Navigation properties
        public virtual Pack Pack { get; set; }
        public virtual Produit Produit { get; set; }

        // Propriétés calculées
        [NotMapped]
        public decimal SousTotalIndividuel => (Produit?.PrixVenteUnitaire ?? 0) * Quantite;
    }
}
