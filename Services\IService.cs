using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SupermarketManagement.Services
{
    /// <summary>
    /// Interface de base pour tous les services
    /// </summary>
    public interface IService
    {
        /// <summary>
        /// Initialise le service
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// Libère les ressources du service
        /// </summary>
        void Dispose();
    }

    /// <summary>
    /// Résultat d'opération de service
    /// </summary>
    /// <typeparam name="T">Type de données retournées</typeparam>
    public class ServiceResult<T>
    {
        /// <summary>
        /// Indique si l'opération a réussi
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Message de résultat
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Données retournées
        /// </summary>
        public T Data { get; set; }

        /// <summary>
        /// Liste des erreurs
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Code d'erreur
        /// </summary>
        public string ErrorCode { get; set; }

        /// <summary>
        /// Exception associée
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// Crée un résultat de succès
        /// </summary>
        /// <param name="data">Données</param>
        /// <param name="message">Message</param>
        /// <returns>Résultat de succès</returns>
        public static ServiceResult<T> CreateSuccess(T data, string message = null)
        {
            return new ServiceResult<T>
            {
                Success = true,
                Data = data,
                Message = message ?? "Opération réussie"
            };
        }

        /// <summary>
        /// Crée un résultat d'erreur
        /// </summary>
        /// <param name="message">Message d'erreur</param>
        /// <param name="errorCode">Code d'erreur</param>
        /// <param name="exception">Exception</param>
        /// <returns>Résultat d'erreur</returns>
        public static ServiceResult<T> CreateError(string message, string errorCode = null, Exception exception = null)
        {
            return new ServiceResult<T>
            {
                Success = false,
                Message = message,
                ErrorCode = errorCode,
                Exception = exception,
                Errors = new List<string> { message }
            };
        }

        /// <summary>
        /// Crée un résultat d'erreur avec plusieurs erreurs
        /// </summary>
        /// <param name="errors">Liste des erreurs</param>
        /// <param name="message">Message principal</param>
        /// <param name="errorCode">Code d'erreur</param>
        /// <returns>Résultat d'erreur</returns>
        public static ServiceResult<T> CreateError(List<string> errors, string message = null, string errorCode = null)
        {
            return new ServiceResult<T>
            {
                Success = false,
                Message = message ?? "Erreurs de validation",
                ErrorCode = errorCode,
                Errors = errors ?? new List<string>()
            };
        }

        /// <summary>
        /// Ajoute une erreur au résultat
        /// </summary>
        /// <param name="error">Erreur à ajouter</param>
        public void AddError(string error)
        {
            Errors.Add(error);
            Success = false;
        }

        /// <summary>
        /// Indique si le résultat contient des erreurs
        /// </summary>
        public bool HasErrors => Errors.Count > 0;
    }

    /// <summary>
    /// Résultat d'opération simple (sans données)
    /// </summary>
    public class ServiceResult : ServiceResult<object>
    {
        /// <summary>
        /// Crée un résultat de succès simple
        /// </summary>
        /// <param name="message">Message</param>
        /// <returns>Résultat de succès</returns>
        public static ServiceResult CreateSuccess(string message = null)
        {
            return new ServiceResult
            {
                Success = true,
                Message = message ?? "Opération réussie"
            };
        }

        /// <summary>
        /// Crée un résultat d'erreur simple
        /// </summary>
        /// <param name="message">Message d'erreur</param>
        /// <param name="errorCode">Code d'erreur</param>
        /// <param name="exception">Exception</param>
        /// <returns>Résultat d'erreur</returns>
        public static new ServiceResult CreateError(string message, string errorCode = null, Exception exception = null)
        {
            return new ServiceResult
            {
                Success = false,
                Message = message,
                ErrorCode = errorCode,
                Exception = exception,
                Errors = new List<string> { message }
            };
        }
    }

    /// <summary>
    /// Interface pour les services de validation
    /// </summary>
    /// <typeparam name="T">Type d'entité à valider</typeparam>
    public interface IValidationService<T>
    {
        /// <summary>
        /// Valide une entité
        /// </summary>
        /// <param name="entity">Entité à valider</param>
        /// <returns>Résultat de validation</returns>
        Task<ServiceResult> ValidateAsync(T entity);

        /// <summary>
        /// Valide une entité pour la création
        /// </summary>
        /// <param name="entity">Entité à valider</param>
        /// <returns>Résultat de validation</returns>
        Task<ServiceResult> ValidateForCreateAsync(T entity);

        /// <summary>
        /// Valide une entité pour la mise à jour
        /// </summary>
        /// <param name="entity">Entité à valider</param>
        /// <returns>Résultat de validation</returns>
        Task<ServiceResult> ValidateForUpdateAsync(T entity);

        /// <summary>
        /// Valide une entité pour la suppression
        /// </summary>
        /// <param name="entityId">ID de l'entité</param>
        /// <returns>Résultat de validation</returns>
        Task<ServiceResult> ValidateForDeleteAsync(int entityId);
    }

    /// <summary>
    /// Interface pour les services de calcul
    /// </summary>
    public interface ICalculationService
    {
        /// <summary>
        /// Calcule le montant TTC à partir du montant HT
        /// </summary>
        /// <param name="montantHT">Montant HT</param>
        /// <param name="tauxTVA">Taux de TVA</param>
        /// <returns>Montant TTC</returns>
        decimal CalculerMontantTTC(decimal montantHT, decimal tauxTVA);

        /// <summary>
        /// Calcule le montant HT à partir du montant TTC
        /// </summary>
        /// <param name="montantTTC">Montant TTC</param>
        /// <param name="tauxTVA">Taux de TVA</param>
        /// <returns>Montant HT</returns>
        decimal CalculerMontantHT(decimal montantTTC, decimal tauxTVA);

        /// <summary>
        /// Calcule le montant de la TVA
        /// </summary>
        /// <param name="montantHT">Montant HT</param>
        /// <param name="tauxTVA">Taux de TVA</param>
        /// <returns>Montant de la TVA</returns>
        decimal CalculerMontantTVA(decimal montantHT, decimal tauxTVA);

        /// <summary>
        /// Calcule une remise
        /// </summary>
        /// <param name="montant">Montant de base</param>
        /// <param name="pourcentageRemise">Pourcentage de remise</param>
        /// <param name="montantRemise">Montant de remise fixe</param>
        /// <returns>Montant de la remise</returns>
        decimal CalculerRemise(decimal montant, decimal pourcentageRemise = 0, decimal montantRemise = 0);

        /// <summary>
        /// Arrondit un montant selon les règles comptables
        /// </summary>
        /// <param name="montant">Montant à arrondir</param>
        /// <param name="decimales">Nombre de décimales</param>
        /// <returns>Montant arrondi</returns>
        decimal ArrondirMontant(decimal montant, int decimales = 2);
    }

    /// <summary>
    /// Interface pour les services de notification
    /// </summary>
    public interface INotificationService
    {
        /// <summary>
        /// Envoie une notification d'information
        /// </summary>
        /// <param name="titre">Titre</param>
        /// <param name="message">Message</param>
        void ShowInfo(string titre, string message);

        /// <summary>
        /// Envoie une notification de succès
        /// </summary>
        /// <param name="titre">Titre</param>
        /// <param name="message">Message</param>
        void ShowSuccess(string titre, string message);

        /// <summary>
        /// Envoie une notification d'avertissement
        /// </summary>
        /// <param name="titre">Titre</param>
        /// <param name="message">Message</param>
        void ShowWarning(string titre, string message);

        /// <summary>
        /// Envoie une notification d'erreur
        /// </summary>
        /// <param name="titre">Titre</param>
        /// <param name="message">Message</param>
        void ShowError(string titre, string message);

        /// <summary>
        /// Demande une confirmation à l'utilisateur
        /// </summary>
        /// <param name="titre">Titre</param>
        /// <param name="message">Message</param>
        /// <returns>True si l'utilisateur confirme</returns>
        bool ShowConfirmation(string titre, string message);
    }
}
