using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Modèle pour les fournisseurs
    /// </summary>
    [Table("fournisseurs")]
    public class Fournisseur : BaseEntity, IActivatable, ICodedEntity, INamedEntity
    {
        [Required]
        [StringLength(50)]
        [Column("code_fournisseur")]
        public string Code { get; set; }

        [Required]
        [StringLength(255)]
        [Column("nom")]
        public string Nom { get; set; }

        [StringLength(255)]
        [Column("raison_sociale")]
        public string RaisonSociale { get; set; }

        [Column("adresse")]
        public string Adresse { get; set; }

        [StringLength(100)]
        [Column("ville")]
        public string Ville { get; set; }

        [StringLength(100)]
        [Column("wilaya")]
        public string Wilaya { get; set; }

        [StringLength(20)]
        [Column("code_postal")]
        public string CodePostal { get; set; }

        [StringLength(50)]
        [Column("telephone")]
        public string Telephone { get; set; }

        [StringLength(100)]
        [Column("email")]
        public string Email { get; set; }

        [Required]
        [StringLength(50)]
        [Column("nif")]
        public string NIF { get; set; }

        [StringLength(50)]
        [Column("nis")]
        public string NIS { get; set; }

        [StringLength(50)]
        [Column("rc")]
        public string RC { get; set; }

        [StringLength(50)]
        [Column("art")]
        public string ART { get; set; }

        [Column("solde_initial")]
        public decimal SoldeInitial { get; set; } = 0.00m;

        [Column("solde_actuel")]
        public decimal SoldeActuel { get; set; } = 0.00m;

        [Column("delai_paiement")]
        public int DelaiPaiement { get; set; } = 30;

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        [Column("date_modification")]
        public new DateTime DateModification { get; set; } = DateTime.Now;

        // Propriété calculée pour le nom d'affichage
        [NotMapped]
        public string NomAffichage => !string.IsNullOrEmpty(RaisonSociale) ? RaisonSociale : Nom;
    }
}
