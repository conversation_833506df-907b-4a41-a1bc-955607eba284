<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>Système de Gestion Supermarché</AssemblyTitle>
    <AssemblyDescription>Application de gestion complète pour supermarché - Algérie</AssemblyDescription>
    <AssemblyCompany>Votre Entreprise</AssemblyCompany>
    <AssemblyProduct>SupermarketManagement</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="MySql.Data" Version="8.2.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageReference Include="NLog" Version="5.2.8" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.3.8" />
  </ItemGroup>

  <ItemGroup>
    <None Update="App.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Backups\" />
    <Folder Include="Images\Company\" />
    <Folder Include="Images\Products\" />
    <Folder Include="Logs\" />
    <Folder Include="Reports\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Forms\**\*.Designer.cs">
      <DependentUpon>%(Filename).cs</DependentUpon>
    </Compile>
  </ItemGroup>

</Project>
