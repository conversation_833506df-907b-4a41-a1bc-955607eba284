using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Énumération des types de client
    /// </summary>
    public enum TypeClient
    {
        Particulier,
        Entreprise
    }

    /// <summary>
    /// Modèle pour les clients
    /// </summary>
    [Table("clients")]
    public class Client : BaseEntity, IActivatable, ICodedEntity, INamedEntity
    {
        [Required]
        [StringLength(50)]
        [Column("code_client")]
        public string Code { get; set; }

        [Required]
        [StringLength(255)]
        [Column("nom")]
        public string Nom { get; set; }

        [StringLength(255)]
        [Column("prenom")]
        public string Prenom { get; set; }

        [StringLength(255)]
        [Column("raison_sociale")]
        public string RaisonSociale { get; set; }

        [Column("type_client")]
        public TypeClient TypeClient { get; set; } = TypeClient.Particulier;

        [Column("adresse")]
        public string Adresse { get; set; }

        [StringLength(100)]
        [Column("ville")]
        public string Ville { get; set; }

        [StringLength(100)]
        [Column("wilaya")]
        public string Wilaya { get; set; }

        [StringLength(20)]
        [Column("code_postal")]
        public string CodePostal { get; set; }

        [StringLength(50)]
        [Column("telephone")]
        public string Telephone { get; set; }

        [StringLength(100)]
        [Column("email")]
        public string Email { get; set; }

        [StringLength(50)]
        [Column("nif")]
        public string NIF { get; set; }

        [StringLength(50)]
        [Column("nis")]
        public string NIS { get; set; }

        [StringLength(50)]
        [Column("rc")]
        public string RC { get; set; }

        [StringLength(50)]
        [Column("art")]
        public string ART { get; set; }

        [Column("solde_initial")]
        public decimal SoldeInitial { get; set; } = 0.00m;

        [Column("solde_actuel")]
        public decimal SoldeActuel { get; set; } = 0.00m;

        [Column("limite_credit")]
        public decimal LimiteCredit { get; set; } = 0.00m;

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        [Column("date_modification")]
        public new DateTime DateModification { get; set; } = DateTime.Now;

        // Propriété calculée pour le nom complet
        [NotMapped]
        public string NomComplet => TypeClient == TypeClient.Entreprise 
            ? RaisonSociale 
            : $"{Prenom} {Nom}".Trim();
    }
}
