using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using SupermarketManagement.Models;
using SupermarketManagement.Repositories;
using SupermarketManagement.Services;

namespace SupermarketManagement.Forms.Clients
{
    /// <summary>
    /// Formulaire d'ajout/modification de client
    /// </summary>
    public partial class FrmClientsAddEdit : Form
    {
        private readonly IClientRepository _clientRepository;
        private readonly INotificationService _notificationService;
        private readonly Client _client;
        private readonly bool _isEditMode;

        // Contrôles du formulaire
        private TextBox txtCode, txtNom, txtPrenom, txtRaisonSociale;
        private TextBox txtAdresse, txtVille, txtWilaya, txtCodePostal;
        private TextBox txtTelephone, txtEmail;
        private TextBox txtNIF, txtNIS, txtRC, txtART;
        private NumericUpDown nudSoldeInitial, nudLimiteCredit;
        private ComboBox cmbTypeClient;
        private CheckBox chkActif;
        private Button btnSave, btnCancel, btnGenerateCode;
        private GroupBox grpIdentification, grpAdresse, grpContact, grpFiscal, grpFinancier;

        public FrmClientsAddEdit(IClientRepository clientRepository, INotificationService notificationService, Client client = null)
        {
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _client = client;
            _isEditMode = client != null;

            InitializeComponent();
            InitializeForm();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configuration du formulaire
            this.Text = _isEditMode ? "Modifier le Client" : "Ajouter un Client";
            this.Size = new Size(800, 650);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();

            this.ResumeLayout(false);
        }

        private void InitializeForm()
        {
            this.Load += FrmClientsAddEdit_Load;
        }

        private void CreateControls()
        {
            // Panel principal avec scroll
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };

            // Groupe Identification
            grpIdentification = CreateGroupBox("Identification", new Point(0, 0), new Size(740, 120));

            var lblCode = CreateLabel("Code Client :", new Point(20, 30));
            txtCode = CreateTextBox(new Point(120, 27), new Size(150, 25));
            btnGenerateCode = CreateButton("Générer", new Point(280, 26), new Size(80, 27), Color.FromArgb(108, 117, 125));
            btnGenerateCode.Click += BtnGenerateCode_Click;

            var lblTypeClient = CreateLabel("Type :", new Point(380, 30));
            cmbTypeClient = CreateComboBox(new Point(450, 27), new Size(120, 25));
            cmbTypeClient.Items.AddRange(new[] { "Particulier", "Entreprise" });
            cmbTypeClient.SelectedIndex = 0;
            cmbTypeClient.SelectedIndexChanged += CmbTypeClient_SelectedIndexChanged;

            var lblNom = CreateLabel("Nom :", new Point(20, 65));
            txtNom = CreateTextBox(new Point(120, 62), new Size(200, 25));

            var lblPrenom = CreateLabel("Prénom :", new Point(340, 65));
            txtPrenom = CreateTextBox(new Point(400, 62), new Size(200, 25));

            var lblRaisonSociale = CreateLabel("Raison Sociale :", new Point(20, 65));
            txtRaisonSociale = CreateTextBox(new Point(120, 62), new Size(480, 25));
            txtRaisonSociale.Visible = false;

            grpIdentification.Controls.AddRange(new Control[]
            {
                lblCode, txtCode, btnGenerateCode, lblTypeClient, cmbTypeClient,
                lblNom, txtNom, lblPrenom, txtPrenom, lblRaisonSociale, txtRaisonSociale
            });

            // Groupe Adresse
            grpAdresse = CreateGroupBox("Adresse", new Point(0, 130), new Size(740, 120));

            var lblAdresse = CreateLabel("Adresse :", new Point(20, 30));
            txtAdresse = CreateTextBox(new Point(120, 27), new Size(580, 25));

            var lblVille = CreateLabel("Ville :", new Point(20, 65));
            txtVille = CreateTextBox(new Point(120, 62), new Size(150, 25));

            var lblWilaya = CreateLabel("Wilaya :", new Point(290, 65));
            txtWilaya = CreateTextBox(new Point(350, 62), new Size(150, 25));

            var lblCodePostal = CreateLabel("Code Postal :", new Point(520, 65));
            txtCodePostal = CreateTextBox(new Point(600, 62), new Size(100, 25));

            grpAdresse.Controls.AddRange(new Control[]
            {
                lblAdresse, txtAdresse, lblVille, txtVille, lblWilaya, txtWilaya, lblCodePostal, txtCodePostal
            });

            // Groupe Contact
            grpContact = CreateGroupBox("Contact", new Point(0, 260), new Size(740, 80));

            var lblTelephone = CreateLabel("Téléphone :", new Point(20, 30));
            txtTelephone = CreateTextBox(new Point(120, 27), new Size(200, 25));

            var lblEmail = CreateLabel("Email :", new Point(340, 30));
            txtEmail = CreateTextBox(new Point(400, 27), new Size(300, 25));

            grpContact.Controls.AddRange(new Control[]
            {
                lblTelephone, txtTelephone, lblEmail, txtEmail
            });

            // Groupe Informations Fiscales
            grpFiscal = CreateGroupBox("Informations Fiscales", new Point(0, 350), new Size(740, 120));

            var lblNIF = CreateLabel("NIF :", new Point(20, 30));
            txtNIF = CreateTextBox(new Point(120, 27), new Size(150, 25));

            var lblNIS = CreateLabel("NIS :", new Point(290, 30));
            txtNIS = CreateTextBox(new Point(350, 27), new Size(150, 25));

            var lblRC = CreateLabel("RC :", new Point(20, 65));
            txtRC = CreateTextBox(new Point(120, 62), new Size(150, 25));

            var lblART = CreateLabel("ART :", new Point(290, 65));
            txtART = CreateTextBox(new Point(350, 62), new Size(200, 25));

            grpFiscal.Controls.AddRange(new Control[]
            {
                lblNIF, txtNIF, lblNIS, txtNIS, lblRC, txtRC, lblART, txtART
            });

            // Groupe Informations Financières
            grpFinancier = CreateGroupBox("Informations Financières", new Point(0, 480), new Size(740, 80));

            var lblSoldeInitial = CreateLabel("Solde Initial :", new Point(20, 30));
            nudSoldeInitial = CreateNumericUpDown(new Point(120, 27), new Size(150, 25));

            var lblLimiteCredit = CreateLabel("Limite Crédit :", new Point(290, 30));
            nudLimiteCredit = CreateNumericUpDown(new Point(390, 27), new Size(150, 25));

            grpFinancier.Controls.AddRange(new Control[]
            {
                lblSoldeInitial, nudSoldeInitial, lblLimiteCredit, nudLimiteCredit
            });

            // Statut
            var grpStatut = CreateGroupBox("Statut", new Point(0, 570), new Size(200, 60));

            chkActif = new CheckBox
            {
                Text = "Client actif",
                Location = new Point(20, 25),
                Size = new Size(100, 25),
                Checked = true,
                Font = new Font("Segoe UI", 9)
            };

            grpStatut.Controls.Add(chkActif);

            // Boutons d'action
            var btnPanel = new Panel
            {
                Location = new Point(0, 580),
                Size = new Size(740, 50),
                BackColor = Color.FromArgb(248, 249, 250)
            };

            btnSave = CreateButton("Enregistrer", new Point(550, 10), new Size(90, 30), Color.FromArgb(40, 167, 69));
            btnCancel = CreateButton("Annuler", new Point(650, 10), new Size(90, 30), Color.FromArgb(108, 117, 125));

            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;

            btnPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });

            // Ajout des contrôles au panel principal
            mainPanel.Controls.AddRange(new Control[]
            {
                grpIdentification, grpAdresse, grpContact, grpFiscal, grpFinancier, grpStatut, btnPanel
            });

            this.Controls.Add(mainPanel);
        }

        private GroupBox CreateGroupBox(string text, Point location, Size size)
        {
            return new GroupBox
            {
                Text = text,
                Location = location,
                Size = size,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 122, 183)
            };
        }

        private Label CreateLabel(string text, Point location)
        {
            return new Label
            {
                Text = text,
                Location = location,
                Size = new Size(90, 20),
                Font = new Font("Segoe UI", 9),
                TextAlign = ContentAlignment.MiddleLeft
            };
        }

        private TextBox CreateTextBox(Point location, Size size)
        {
            return new TextBox
            {
                Location = location,
                Size = size,
                Font = new Font("Segoe UI", 9)
            };
        }

        private ComboBox CreateComboBox(Point location, Size size)
        {
            return new ComboBox
            {
                Location = location,
                Size = size,
                Font = new Font("Segoe UI", 9),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
        }

        private NumericUpDown CreateNumericUpDown(Point location, Size size)
        {
            return new NumericUpDown
            {
                Location = location,
                Size = size,
                Font = new Font("Segoe UI", 9),
                DecimalPlaces = 2,
                Maximum = 999999999,
                Minimum = -999999999
            };
        }

        private Button CreateButton(string text, Point location, Size size, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = size,
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                UseVisualStyleBackColor = false
            };
            button.FlatAppearance.BorderSize = 0;
            return button;
        }

        private async void FrmClientsAddEdit_Load(object sender, EventArgs e)
        {
            if (_isEditMode)
            {
                LoadClientData();
            }
            else
            {
                await GenerateCodeAsync();
            }

            UpdateFieldsVisibility();
        }

        private void LoadClientData()
        {
            if (_client == null) return;

            txtCode.Text = _client.Code;
            cmbTypeClient.SelectedItem = _client.TypeClient == TypeClient.Particulier ? "Particulier" : "Entreprise";
            txtNom.Text = _client.Nom;
            txtPrenom.Text = _client.Prenom;
            txtRaisonSociale.Text = _client.RaisonSociale;
            txtAdresse.Text = _client.Adresse;
            txtVille.Text = _client.Ville;
            txtWilaya.Text = _client.Wilaya;
            txtCodePostal.Text = _client.CodePostal;
            txtTelephone.Text = _client.Telephone;
            txtEmail.Text = _client.Email;
            txtNIF.Text = _client.NIF;
            txtNIS.Text = _client.NIS;
            txtRC.Text = _client.RC;
            txtART.Text = _client.ART;
            nudSoldeInitial.Value = _client.SoldeInitial;
            nudLimiteCredit.Value = _client.LimiteCredit;
            chkActif.Checked = _client.Actif;
        }

        private async Task GenerateCodeAsync()
        {
            try
            {
                var code = await _clientRepository.GenerateNextCodeAsync();
                txtCode.Text = code;
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors de la génération du code : {ex.Message}");
            }
        }

        private async void BtnGenerateCode_Click(object sender, EventArgs e)
        {
            await GenerateCodeAsync();
        }

        private void CmbTypeClient_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateFieldsVisibility();
        }

        private void UpdateFieldsVisibility()
        {
            var isEntreprise = cmbTypeClient.SelectedItem?.ToString() == "Entreprise";

            // Masquer/afficher les champs selon le type de client
            txtNom.Visible = !isEntreprise;
            txtPrenom.Visible = !isEntreprise;
            txtRaisonSociale.Visible = isEntreprise;

            // Ajuster les labels
            var lblNom = grpIdentification.Controls.OfType<Label>().FirstOrDefault(l => l.Text == "Nom :");
            var lblRaisonSociale = grpIdentification.Controls.OfType<Label>().FirstOrDefault(l => l.Text == "Raison Sociale :");
            
            if (lblNom != null) lblNom.Visible = !isEntreprise;
            if (lblRaisonSociale != null) lblRaisonSociale.Visible = isEntreprise;

            // Rendre obligatoires certains champs pour les entreprises
            if (isEntreprise)
            {
                txtNIF.BackColor = Color.FromArgb(255, 243, 205); // Jaune clair pour indiquer obligatoire
            }
            else
            {
                txtNIF.BackColor = Color.White;
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                var client = _isEditMode ? _client : new Client();

                // Remplir les propriétés
                client.Code = txtCode.Text.Trim();
                client.TypeClient = cmbTypeClient.SelectedItem.ToString() == "Particulier" ? TypeClient.Particulier : TypeClient.Entreprise;
                
                if (client.TypeClient == TypeClient.Particulier)
                {
                    client.Nom = txtNom.Text.Trim();
                    client.Prenom = txtPrenom.Text.Trim();
                    client.RaisonSociale = null;
                }
                else
                {
                    client.RaisonSociale = txtRaisonSociale.Text.Trim();
                    client.Nom = txtRaisonSociale.Text.Trim(); // Pour la recherche
                    client.Prenom = null;
                }

                client.Adresse = txtAdresse.Text.Trim();
                client.Ville = txtVille.Text.Trim();
                client.Wilaya = txtWilaya.Text.Trim();
                client.CodePostal = txtCodePostal.Text.Trim();
                client.Telephone = txtTelephone.Text.Trim();
                client.Email = txtEmail.Text.Trim();
                client.NIF = txtNIF.Text.Trim();
                client.NIS = txtNIS.Text.Trim();
                client.RC = txtRC.Text.Trim();
                client.ART = txtART.Text.Trim();
                client.SoldeInitial = nudSoldeInitial.Value;
                client.SoldeActuel = _isEditMode ? client.SoldeActuel : nudSoldeInitial.Value;
                client.LimiteCredit = nudLimiteCredit.Value;
                client.Actif = chkActif.Checked;

                if (_isEditMode)
                {
                    await _clientRepository.UpdateAsync(client);
                    _notificationService.ShowSuccess("Succès", "Client modifié avec succès");
                }
                else
                {
                    await _clientRepository.AddAsync(client);
                    _notificationService.ShowSuccess("Succès", "Client ajouté avec succès");
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors de l'enregistrement : {ex.Message}");
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                _notificationService.ShowWarning("Validation", "Le code client est requis");
                txtCode.Focus();
                return false;
            }

            var isEntreprise = cmbTypeClient.SelectedItem?.ToString() == "Entreprise";

            if (isEntreprise)
            {
                if (string.IsNullOrWhiteSpace(txtRaisonSociale.Text))
                {
                    _notificationService.ShowWarning("Validation", "La raison sociale est requise pour une entreprise");
                    txtRaisonSociale.Focus();
                    return false;
                }

                if (string.IsNullOrWhiteSpace(txtNIF.Text))
                {
                    _notificationService.ShowWarning("Validation", "Le NIF est requis pour une entreprise");
                    txtNIF.Focus();
                    return false;
                }
            }
            else
            {
                if (string.IsNullOrWhiteSpace(txtNom.Text))
                {
                    _notificationService.ShowWarning("Validation", "Le nom est requis");
                    txtNom.Focus();
                    return false;
                }
            }

            // Validation de l'email si fourni
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(txtEmail.Text);
                    if (addr.Address != txtEmail.Text)
                    {
                        _notificationService.ShowWarning("Validation", "Format d'email invalide");
                        txtEmail.Focus();
                        return false;
                    }
                }
                catch
                {
                    _notificationService.ShowWarning("Validation", "Format d'email invalide");
                    txtEmail.Focus();
                    return false;
                }
            }

            return true;
        }
    }
}
