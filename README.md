# 🏪 Système de Gestion Supermarché - Algérie

## 📋 Description

Application complète de gestion de supermarché développée en **C# WinForms** avec **MySQL** et **Dapper ORM**, spécialement conçue pour le marché algérien avec support complet des réglementations locales.

## ✨ Fonctionnalités Principales

### 🛍️ Gestion des Ventes
- Point de vente (POS) avec interface intuitive
- Support des codes-barres multiples
- Calcul automatique de la TVA (19% par défaut)
- **Calcul automatique du droit de timbre algérien** pour les paiements en espèces
- Gestion des promotions et remises
- Factures, proforma et bons de livraison

### 📦 Gestion des Produits
- Catalogue produits avec catégories hiérarchiques
- Gestion multi-unités (pièce, kg, litre, etc.)
- Codes-barres multiples par produit
- Calcul automatique du PMP (Prix Moyen Pondéré)
- Gestion des packs et fardeaux
- Alertes de stock automatiques

### 👥 Gestion Clients/Fournisseurs
- Support complet **NIF, NIS, RC, ART** (réglementation algérienne)
- Gestion des particuliers et entreprises
- Comptes clients avec limites de crédit
- Historique des transactions

### 💰 Gestion Financière
- Caisses et comptes bancaires
- Paiements clients/fournisseurs
- **Droit de timbre automatique** selon la réglementation algérienne :
  - ≤ 300 DA : Exonéré
  - 300-30 000 DA : 1 DA
  - 30 000-100 000 DA : 1,5 DA par tranche de 100 DA
- Dépenses et recettes
- Rapports financiers

### 📊 Rapports et Analyses
- Tableau de bord en temps réel
- Rapports de ventes, achats, stock
- Statistiques détaillées
- Export vers Excel/PDF

## 🏗️ Architecture Technique

### Technologies Utilisées
- **Framework** : .NET 8.0 Windows Forms
- **Base de données** : MySQL 8.0+
- **ORM** : Dapper
- **Injection de dépendances** : Microsoft.Extensions.DependencyInjection
- **Logging** : NLog

### Structure du Projet
```
SupermarketManagement/
├── Configuration/          # Configuration de l'application
├── Data/                  # Contexte de données et connexions
├── Models/                # Modèles de données
├── Repositories/          # Couche d'accès aux données
├── Services/              # Logique métier
├── Forms/                 # Interface utilisateur
│   ├── Products/          # Gestion des produits
│   ├── Clients/           # Gestion des clients
│   ├── Sales/             # Gestion des ventes
│   └── Reports/           # Rapports
├── database_supermarket.sql # Script de création de la base
└── App.config             # Configuration de l'application
```

## 🚀 Installation et Configuration

### Prérequis
- Windows 10/11
- .NET 8.0 Runtime
- MySQL Server 8.0+
- Visual Studio 2022 (pour le développement)

### Installation

1. **Cloner le projet**
```bash
git clone https://github.com/votre-repo/supermarket-management.git
cd supermarket-management
```

2. **Configurer la base de données**
```sql
-- Créer la base de données
CREATE DATABASE supermarket_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Exécuter le script de création
mysql -u root -p supermarket_db < database_supermarket.sql
```

3. **Configurer la connexion**
Modifier le fichier `App.config` :
```xml
<connectionStrings>
  <add name="SupermarketDB" 
       connectionString="Server=localhost;Database=supermarket_db;Uid=root;Pwd=******************;" />
</connectionStrings>
```

4. **Compiler et exécuter**
```bash
dotnet build
dotnet run
```

### Connexion par défaut
- **Utilisateur** : `admin`
- **Mot de passe** : `password`

## 🇩🇿 Spécificités Algériennes

### Conformité Fiscale
- **NIF** (Numéro d'Identification Fiscale)
- **NIS** (Numéro d'Identification Statistique)
- **RC** (Registre de Commerce)
- **ART** (Article d'Imposition)
- **TVA à 19%** par défaut
- **Droit de timbre** automatique pour paiements espèces

### Localisation
- Interface en **français**
- Format de date : `dd/MM/yyyy`
- Devise : **Dinar Algérien (DZD)**
- Séparateur décimal : virgule (`,`)

## 📱 Utilisation

### Démarrage Rapide

1. **Première connexion**
   - Lancer l'application
   - Se connecter avec admin/password
   - Configurer les paramètres de l'entreprise

2. **Configuration initiale**
   - Ajouter les catégories de produits
   - Configurer les unités de mesure
   - Paramétrer les caisses et banques

3. **Gestion quotidienne**
   - Ajouter les produits
   - Enregistrer les clients/fournisseurs
   - Effectuer les ventes
   - Suivre le stock

### Fonctionnalités Avancées

#### Calcul du Droit de Timbre
```csharp
var calculationService = new CalculationService();
var detailTimbre = calculationService.ObtenirDetailTimbre(montantPaiement);
// Calcul automatique selon la réglementation algérienne
```

#### Gestion Multi-Codes-Barres
- Un produit peut avoir plusieurs codes-barres
- Support des unités différentes par code-barres
- Conversion automatique des quantités

## 🔧 Configuration Avancée

### Paramètres de l'Application
```xml
<appSettings>
  <!-- Paramètres généraux -->
  <add key="DefaultTVARate" value="19.00" />
  <add key="DefaultCurrency" value="DZD" />
  
  <!-- Paramètres de stock -->
  <add key="EnableStockAlerts" value="true" />
  <add key="EnableNegativeStock" value="false" />
  
  <!-- Paramètres de sauvegarde -->
  <add key="EnableAutoBackup" value="true" />
  <add key="AutoBackupIntervalHours" value="24" />
</appSettings>
```

### Personnalisation
- Logos et images personnalisables
- Formats de factures modifiables
- Rapports personnalisés
- Thèmes d'interface

## 🛠️ Développement

### Ajouter un Nouveau Module

1. **Créer le modèle**
```csharp
[Table("ma_table")]
public class MonEntite : BaseEntity, IActivatable
{
    // Propriétés
}
```

2. **Créer le repository**
```csharp
public interface IMonRepository : IActivatableRepository<MonEntite>
{
    // Méthodes spécifiques
}

public class MonRepository : ActivatableRepository<MonEntite>, IMonRepository
{
    // Implémentation
}
```

3. **Créer le service**
```csharp
public interface IMonService : IService
{
    // Méthodes métier
}

public class MonService : IMonService
{
    // Logique métier
}
```

4. **Créer les formulaires**
```
Forms/MonModule/
├── FrmMonModuleList.cs
├── FrmMonModuleList.Designer.cs
├── FrmMonModuleAddEdit.cs
└── FrmMonModuleAddEdit.Designer.cs
```

### Tests
```bash
# Exécuter les tests unitaires
dotnet test

# Tests d'intégration
dotnet test --filter Category=Integration
```

## 📊 Base de Données

### Tables Principales
- `produits` - Catalogue produits
- `clients` - Gestion clients
- `fournisseurs` - Gestion fournisseurs
- `factures_vente` - Factures de vente
- `factures_achat` - Factures d'achat
- `mouvements_stock` - Traçabilité stock
- `mouvements_caisse` - Mouvements financiers

### Vues Utiles
- `vue_etat_stock` - État du stock en temps réel
- `vue_comptes_clients` - Soldes clients
- `vue_tableau_bord_ventes` - Statistiques de vente

## 🔒 Sécurité

### Authentification
- Gestion des utilisateurs et rôles
- Sessions sécurisées
- Logs d'activité

### Sauvegarde
- Sauvegarde automatique programmable
- Export/Import de données
- Historique des sauvegardes

## 📞 Support

### Documentation
- Manuel utilisateur intégré
- Documentation technique
- Exemples de code

### Maintenance
- Logs détaillés avec NLog
- Monitoring des performances
- Outils de diagnostic

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! Veuillez :

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📈 Roadmap

- [ ] Module de comptabilité avancée
- [ ] Interface web responsive
- [ ] API REST
- [ ] Application mobile
- [ ] Intégration e-commerce
- [ ] BI et analytics avancés

---

**Développé avec ❤️ pour les entreprises algériennes**
