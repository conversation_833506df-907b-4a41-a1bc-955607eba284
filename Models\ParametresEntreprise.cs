using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Modèle pour les paramètres de l'entreprise
    /// </summary>
    [Table("parametres_entreprise")]
    public class ParametresEntreprise : BaseEntity
    {
        [Required]
        [StringLength(255)]
        [Column("nom_entreprise")]
        public string NomEntreprise { get; set; }

        [Column("adresse")]
        public string Adresse { get; set; }

        [StringLength(50)]
        [Column("telephone")]
        public string Telephone { get; set; }

        [StringLength(100)]
        [Column("email")]
        public string Email { get; set; }

        [StringLength(50)]
        [Column("nif")]
        public string NIF { get; set; }

        [StringLength(50)]
        [Column("nis")]
        public string NIS { get; set; }

        [StringLength(50)]
        [Column("rc")]
        public string RC { get; set; }

        [StringLength(50)]
        [Column("art")]
        public string ART { get; set; }

        [StringLength(500)]
        [Column("logo_path")]
        public string LogoPath { get; set; }

        [Column("taux_tva_defaut")]
        public decimal TauxTVADefaut { get; set; } = 19.00m;

        [StringLength(10)]
        [Column("devise")]
        public string Devise { get; set; } = "DZD";

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        [Column("date_modification")]
        public new DateTime DateModification { get; set; } = DateTime.Now;
    }
}
