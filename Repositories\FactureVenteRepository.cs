using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;
using SupermarketManagement.Data;
using SupermarketManagement.Models;

namespace SupermarketManagement.Repositories
{
    /// <summary>
    /// Interface pour le repository des factures de vente
    /// </summary>
    public interface IFactureVenteRepository : IRepository<FactureVente>
    {
        /// <summary>
        /// Obtient une facture par son numéro
        /// </summary>
        /// <param name="numeroFacture">Numéro de la facture</param>
        /// <returns>Facture ou null</returns>
        Task<FactureVente> GetByNumeroAsync(string numeroFacture);

        /// <summary>
        /// Obtient les factures d'un client
        /// </summary>
        /// <param name="clientId">ID du client</param>
        /// <returns>Liste des factures</returns>
        Task<IEnumerable<FactureVente>> GetByClientAsync(int clientId);

        /// <summary>
        /// Obtient les factures d'une période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des factures</returns>
        Task<IEnumerable<FactureVente>> GetByPeriodeAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Obtient les détails d'une facture
        /// </summary>
        /// <param name="factureId">ID de la facture</param>
        /// <returns>Liste des détails</returns>
        Task<IEnumerable<DetailFactureVente>> GetDetailsAsync(int factureId);

        /// <summary>
        /// Ajoute une facture avec ses détails
        /// </summary>
        /// <param name="facture">Facture à ajouter</param>
        /// <param name="details">Détails de la facture</param>
        /// <param name="transaction">Transaction optionnelle</param>
        /// <returns>ID de la facture créée</returns>
        Task<int> AddWithDetailsAsync(FactureVente facture, IEnumerable<DetailFactureVente> details, IDbTransaction transaction = null);

        /// <summary>
        /// Met à jour le statut d'une facture
        /// </summary>
        /// <param name="factureId">ID de la facture</param>
        /// <param name="statut">Nouveau statut</param>
        /// <returns>True si la mise à jour réussit</returns>
        Task<bool> UpdateStatutAsync(int factureId, StatutFacture statut);

        /// <summary>
        /// Génère le prochain numéro de facture
        /// </summary>
        /// <param name="typeFacture">Type de facture</param>
        /// <returns>Nouveau numéro de facture</returns>
        Task<string> GenerateNextNumeroAsync(TypeFacture typeFacture);

        /// <summary>
        /// Obtient le chiffre d'affaires d'une période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Chiffre d'affaires</returns>
        Task<decimal> GetChiffreAffairesAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Obtient les statistiques de vente du jour
        /// </summary>
        /// <param name="date">Date</param>
        /// <returns>Statistiques</returns>
        Task<StatistiquesVente> GetStatistiquesJourAsync(DateTime date);

        /// <summary>
        /// Annule une facture
        /// </summary>
        /// <param name="factureId">ID de la facture</param>
        /// <param name="motifAnnulation">Motif d'annulation</param>
        /// <returns>True si l'annulation réussit</returns>
        Task<bool> AnnulerFactureAsync(int factureId, string motifAnnulation);
    }

    /// <summary>
    /// Repository pour la gestion des factures de vente
    /// </summary>
    public class FactureVenteRepository : BaseRepository<FactureVente>, IFactureVenteRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        public FactureVenteRepository(DapperContext context) : base(context, "factures_vente")
        {
        }

        /// <summary>
        /// Obtient une facture par son numéro
        /// </summary>
        public async Task<FactureVente> GetByNumeroAsync(string numeroFacture)
        {
            var sql = @"
                SELECT fv.*, c.nom as ClientNom, c.prenom as ClientPrenom, u.nom_complet as UtilisateurNom
                FROM factures_vente fv
                LEFT JOIN clients c ON fv.client_id = c.id
                LEFT JOIN utilisateurs u ON fv.utilisateur_id = u.id
                WHERE fv.numero_facture = @NumeroFacture";

            return await _context.QueryFirstOrDefaultAsync<FactureVente>(sql, new { NumeroFacture = numeroFacture });
        }

        /// <summary>
        /// Obtient les factures d'un client
        /// </summary>
        public async Task<IEnumerable<FactureVente>> GetByClientAsync(int clientId)
        {
            var sql = @"
                SELECT fv.*, c.nom as ClientNom, c.prenom as ClientPrenom, u.nom_complet as UtilisateurNom
                FROM factures_vente fv
                LEFT JOIN clients c ON fv.client_id = c.id
                LEFT JOIN utilisateurs u ON fv.utilisateur_id = u.id
                WHERE fv.client_id = @ClientId
                ORDER BY fv.date_facture DESC, fv.heure_facture DESC";

            return await _context.QueryAsync<FactureVente>(sql, new { ClientId = clientId });
        }

        /// <summary>
        /// Obtient les factures d'une période
        /// </summary>
        public async Task<IEnumerable<FactureVente>> GetByPeriodeAsync(DateTime dateDebut, DateTime dateFin)
        {
            var sql = @"
                SELECT fv.*, c.nom as ClientNom, c.prenom as ClientPrenom, u.nom_complet as UtilisateurNom
                FROM factures_vente fv
                LEFT JOIN clients c ON fv.client_id = c.id
                LEFT JOIN utilisateurs u ON fv.utilisateur_id = u.id
                WHERE fv.date_facture BETWEEN @DateDebut AND @DateFin
                ORDER BY fv.date_facture DESC, fv.heure_facture DESC";

            return await _context.QueryAsync<FactureVente>(sql, new { DateDebut = dateDebut, DateFin = dateFin });
        }

        /// <summary>
        /// Obtient les détails d'une facture
        /// </summary>
        public async Task<IEnumerable<DetailFactureVente>> GetDetailsAsync(int factureId)
        {
            var sql = @"
                SELECT dfv.*, p.nom as ProduitNom, p.code_produit as ProduitCode, u.nom as UniteNom, u.symbole as UniteSymbole
                FROM details_facture_vente dfv
                LEFT JOIN produits p ON dfv.produit_id = p.id
                LEFT JOIN unites_mesure u ON dfv.unite_id = u.id
                WHERE dfv.facture_id = @FactureId
                ORDER BY dfv.id";

            return await _context.QueryAsync<DetailFactureVente>(sql, new { FactureId = factureId });
        }

        /// <summary>
        /// Ajoute une facture avec ses détails
        /// </summary>
        public async Task<int> AddWithDetailsAsync(FactureVente facture, IEnumerable<DetailFactureVente> details, IDbTransaction transaction = null)
        {
            var useLocalTransaction = transaction == null;
            if (useLocalTransaction)
                transaction = await BeginTransactionAsync();

            try
            {
                // Ajouter la facture
                var factureId = await AddAsync(facture);

                // Ajouter les détails
                var detailSql = @"
                    INSERT INTO details_facture_vente (
                        facture_id, produit_id, unite_id, quantite, prix_unitaire_ht, prix_unitaire_ttc,
                        remise_pourcentage, remise_montant, taux_tva, montant_tva, sous_total_ht, sous_total_ttc
                    ) VALUES (
                        @FactureId, @ProduitId, @UniteId, @Quantite, @PrixUnitaireHT, @PrixUnitaireTTC,
                        @RemisePourcentage, @RemiseMontant, @TauxTVA, @MontantTVA, @SousTotalHT, @SousTotalTTC
                    )";

                foreach (var detail in details)
                {
                    detail.FactureId = factureId;
                    await _context.ExecuteAsync(detailSql, detail, transaction);
                }

                if (useLocalTransaction)
                    await CommitTransactionAsync(transaction);

                return factureId;
            }
            catch
            {
                if (useLocalTransaction)
                    await RollbackTransactionAsync(transaction);
                throw;
            }
            finally
            {
                if (useLocalTransaction)
                    transaction?.Dispose();
            }
        }

        /// <summary>
        /// Met à jour le statut d'une facture
        /// </summary>
        public async Task<bool> UpdateStatutAsync(int factureId, StatutFacture statut)
        {
            var sql = @"
                UPDATE factures_vente 
                SET statut = @Statut, date_modification = @DateModification
                WHERE id = @FactureId";

            var affectedRows = await _context.ExecuteAsync(sql, new 
            { 
                FactureId = factureId, 
                Statut = statut,
                DateModification = DateTime.Now
            });

            return affectedRows > 0;
        }

        /// <summary>
        /// Génère le prochain numéro de facture
        /// </summary>
        public async Task<string> GenerateNextNumeroAsync(TypeFacture typeFacture)
        {
            var prefix = typeFacture switch
            {
                TypeFacture.Vente => "FV",
                TypeFacture.Proforma => "PF",
                TypeFacture.BonLivraison => "BL",
                _ => "FV"
            };

            var year = DateTime.Now.Year;
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTRING(numero_facture, LENGTH(@Prefix) + 5) AS UNSIGNED)), 0) + 1 
                FROM factures_vente 
                WHERE numero_facture LIKE CONCAT(@Prefix, @Year, '%')
                AND type_facture = @TypeFacture";

            var nextNumber = await _context.ExecuteScalarAsync<int>(sql, new 
            { 
                Prefix = prefix, 
                Year = year, 
                TypeFacture = typeFacture 
            });

            return $"{prefix}{year}{nextNumber:D6}"; // Format: FV2024000001
        }

        /// <summary>
        /// Obtient le chiffre d'affaires d'une période
        /// </summary>
        public async Task<decimal> GetChiffreAffairesAsync(DateTime dateDebut, DateTime dateFin)
        {
            var sql = @"
                SELECT COALESCE(SUM(total_ttc), 0)
                FROM factures_vente 
                WHERE date_facture BETWEEN @DateDebut AND @DateFin
                AND statut = @Statut";

            return await _context.ExecuteScalarAsync<decimal>(sql, new 
            { 
                DateDebut = dateDebut, 
                DateFin = dateFin,
                Statut = StatutFacture.Validee
            });
        }

        /// <summary>
        /// Obtient les statistiques de vente du jour
        /// </summary>
        public async Task<StatistiquesVente> GetStatistiquesJourAsync(DateTime date)
        {
            var sql = @"
                SELECT 
                    COUNT(*) as NombreFactures,
                    COALESCE(SUM(total_ttc), 0) as ChiffreAffaires,
                    COALESCE(SUM(sous_total_ht), 0) as TotalHT,
                    COALESCE(SUM(total_tva), 0) as TotalTVA,
                    COALESCE(AVG(total_ttc), 0) as PanierMoyen
                FROM factures_vente 
                WHERE DATE(date_facture) = @Date
                AND statut = @Statut";

            return await _context.QueryFirstOrDefaultAsync<StatistiquesVente>(sql, new 
            { 
                Date = date.Date,
                Statut = StatutFacture.Validee
            });
        }

        /// <summary>
        /// Annule une facture
        /// </summary>
        public async Task<bool> AnnulerFactureAsync(int factureId, string motifAnnulation)
        {
            var transaction = await BeginTransactionAsync();
            try
            {
                // Mettre à jour le statut de la facture
                var updateFactureSql = @"
                    UPDATE factures_vente 
                    SET statut = @Statut, notes = CONCAT(COALESCE(notes, ''), ' - ANNULÉE: ', @Motif), date_modification = @DateModification
                    WHERE id = @FactureId";

                await _context.ExecuteAsync(updateFactureSql, new 
                { 
                    FactureId = factureId, 
                    Statut = StatutFacture.Annulee,
                    Motif = motifAnnulation,
                    DateModification = DateTime.Now
                }, transaction);

                // Ici, vous pourriez ajouter la logique pour restaurer le stock si nécessaire

                await CommitTransactionAsync(transaction);
                return true;
            }
            catch
            {
                await RollbackTransactionAsync(transaction);
                return false;
            }
            finally
            {
                transaction?.Dispose();
            }
        }

        /// <summary>
        /// Génère le SQL d'insertion
        /// </summary>
        protected override string GenerateInsertSql()
        {
            return @"
                INSERT INTO factures_vente (
                    numero_facture, client_id, utilisateur_id, date_facture, heure_facture, type_facture,
                    sous_total_ht, total_remise, total_tva, total_ttc, mode_paiement, montant_paye,
                    montant_rendu, statut, notes, caisse_id, banque_id, date_creation, date_modification
                ) VALUES (
                    @NumeroFacture, @ClientId, @UtilisateurId, @DateFacture, @HeureFacture, @TypeFacture,
                    @SousTotalHT, @TotalRemise, @TotalTVA, @TotalTTC, @ModePaiement, @MontantPaye,
                    @MontantRendu, @Statut, @Notes, @CaisseId, @BanqueId, @DateCreation, @DateModification
                );
                SELECT LAST_INSERT_ID();";
        }

        /// <summary>
        /// Génère le SQL de mise à jour
        /// </summary>
        protected override string GenerateUpdateSql()
        {
            return @"
                UPDATE factures_vente SET 
                    numero_facture = @NumeroFacture,
                    client_id = @ClientId,
                    utilisateur_id = @UtilisateurId,
                    date_facture = @DateFacture,
                    heure_facture = @HeureFacture,
                    type_facture = @TypeFacture,
                    sous_total_ht = @SousTotalHT,
                    total_remise = @TotalRemise,
                    total_tva = @TotalTVA,
                    total_ttc = @TotalTTC,
                    mode_paiement = @ModePaiement,
                    montant_paye = @MontantPaye,
                    montant_rendu = @MontantRendu,
                    statut = @Statut,
                    notes = @Notes,
                    caisse_id = @CaisseId,
                    banque_id = @BanqueId,
                    date_modification = @DateModification
                WHERE id = @Id";
        }

        /// <summary>
        /// Obtient les colonnes de recherche
        /// </summary>
        protected override string[] GetSearchColumns()
        {
            return new[] { "numero_facture", "notes" };
        }
    }

    /// <summary>
    /// Classe pour les statistiques de vente
    /// </summary>
    public class StatistiquesVente
    {
        public int NombreFactures { get; set; }
        public decimal ChiffreAffaires { get; set; }
        public decimal TotalHT { get; set; }
        public decimal TotalTVA { get; set; }
        public decimal PanierMoyen { get; set; }
    }
}
