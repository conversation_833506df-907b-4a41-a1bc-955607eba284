using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SupermarketManagement.Models;
using SupermarketManagement.Repositories;
using SupermarketManagement.Services;

namespace SupermarketManagement.Forms.Clients
{
    /// <summary>
    /// Formulaire de liste des clients
    /// </summary>
    public partial class FrmClientsList : Form
    {
        private readonly IClientRepository _clientRepository;
        private readonly INotificationService _notificationService;
        private DataGridView dgvClients;
        private TextBox txtSearch;
        private Button btnAdd, btnEdit, btnDelete, btnRefresh, btnViewAccount;
        private ComboBox cmbTypeClient, cmbStatus;
        private Label lblTotal, lblTotalSolde;

        public FrmClientsList(IClientRepository clientRepository, INotificationService notificationService)
        {
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));

            InitializeComponent();
            InitializeFormControls();
            InitializeForm();
        }

        private void InitializeFormControls()
        {
            this.SuspendLayout();

            // Configuration du formulaire
            this.Text = "Gestion des Clients";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;

            CreateControls();
            ConfigureDataGridView();

            this.ResumeLayout(false);
        }

        private void InitializeForm()
        {
            this.Load += FrmClientsList_Load;
        }

        private void CreateControls()
        {
            // Panel supérieur pour les contrôles de recherche et actions
            var topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            // Recherche
            var lblSearch = new Label
            {
                Text = "Rechercher :",
                Location = new Point(10, 15),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 9)
            };

            txtSearch = new TextBox
            {
                Location = new Point(95, 12),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 9)
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;

            // Filtre par type de client
            var lblTypeClient = new Label
            {
                Text = "Type :",
                Location = new Point(310, 15),
                Size = new Size(50, 20),
                Font = new Font("Segoe UI", 9)
            };

            cmbTypeClient = new ComboBox
            {
                Location = new Point(365, 12),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9)
            };
            cmbTypeClient.Items.AddRange(new[] { "Tous", "Particulier", "Entreprise" });
            cmbTypeClient.SelectedIndex = 0;
            cmbTypeClient.SelectedIndexChanged += CmbTypeClient_SelectedIndexChanged;

            // Filtre par statut
            var lblStatus = new Label
            {
                Text = "Statut :",
                Location = new Point(500, 15),
                Size = new Size(50, 20),
                Font = new Font("Segoe UI", 9)
            };

            cmbStatus = new ComboBox
            {
                Location = new Point(555, 12),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9)
            };
            cmbStatus.Items.AddRange(new[] { "Tous", "Actif", "Inactif" });
            cmbStatus.SelectedIndex = 0;
            cmbStatus.SelectedIndexChanged += CmbStatus_SelectedIndexChanged;

            // Boutons d'action
            btnAdd = CreateButton("Ajouter", new Point(680, 10), Color.FromArgb(40, 167, 69));
            btnEdit = CreateButton("Modifier", new Point(780, 10), Color.FromArgb(0, 123, 255));
            btnDelete = CreateButton("Supprimer", new Point(880, 10), Color.FromArgb(220, 53, 69));
            btnViewAccount = CreateButton("Compte", new Point(980, 10), Color.FromArgb(255, 193, 7));
            btnRefresh = CreateButton("Actualiser", new Point(1080, 10), Color.FromArgb(108, 117, 125));

            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnViewAccount.Click += BtnViewAccount_Click;
            btnRefresh.Click += BtnRefresh_Click;

            // Labels de statistiques
            lblTotal = new Label
            {
                Location = new Point(10, 50),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 122, 183)
            };

            lblTotalSolde = new Label
            {
                Location = new Point(220, 50),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            topPanel.Controls.AddRange(new Control[]
            {
                lblSearch, txtSearch, lblTypeClient, cmbTypeClient, lblStatus, cmbStatus,
                btnAdd, btnEdit, btnDelete, btnViewAccount, btnRefresh, lblTotal, lblTotalSolde
            });

            // Panel principal pour le DataGridView
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            dgvClients = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 9)
            };

            mainPanel.Controls.Add(dgvClients);

            this.Controls.Add(mainPanel);
            this.Controls.Add(topPanel);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(90, 30),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                UseVisualStyleBackColor = false
            };
        }

        private void ConfigureDataGridView()
        {
            dgvClients.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Code",
                    HeaderText = "Code",
                    DataPropertyName = "Code",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "NomComplet",
                    HeaderText = "Nom / Raison Sociale",
                    DataPropertyName = "NomComplet",
                    Width = 200,
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "TypeClient",
                    HeaderText = "Type",
                    DataPropertyName = "TypeClient",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Telephone",
                    HeaderText = "Téléphone",
                    DataPropertyName = "Telephone",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Email",
                    HeaderText = "Email",
                    DataPropertyName = "Email",
                    Width = 150
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Ville",
                    HeaderText = "Ville",
                    DataPropertyName = "Ville",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "NIF",
                    HeaderText = "NIF",
                    DataPropertyName = "NIF",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "SoldeActuel",
                    HeaderText = "Solde",
                    DataPropertyName = "SoldeActuel",
                    Width = 100,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewCheckBoxColumn
                {
                    Name = "Actif",
                    HeaderText = "Actif",
                    DataPropertyName = "Actif",
                    Width = 60
                }
            });

            // Style des en-têtes
            dgvClients.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(51, 122, 183);
            dgvClients.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvClients.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9, FontStyle.Bold);

            // Style des lignes alternées
            dgvClients.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // Événements
            dgvClients.CellDoubleClick += DgvClients_CellDoubleClick;
            dgvClients.SelectionChanged += DgvClients_SelectionChanged;
            dgvClients.CellFormatting += DgvClients_CellFormatting;
        }

        private async void FrmClientsList_Load(object sender, EventArgs e)
        {
            await LoadClientsAsync();
        }

        private async Task LoadClientsAsync()
        {
            try
            {
                var clients = await _clientRepository.GetAllAsync();
                dgvClients.DataSource = clients.ToList();
                
                UpdateStatistics(clients);
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors du chargement des clients : {ex.Message}");
            }
        }

        private void UpdateStatistics(System.Collections.Generic.IEnumerable<Client> clients)
        {
            var clientsList = clients.ToList();
            var totalClients = clientsList.Count;
            var totalSolde = clientsList.Sum(c => c.SoldeActuel);

            lblTotal.Text = $"Total : {totalClients} clients";
            lblTotalSolde.Text = $"Solde total : {totalSolde:N2} DA";

            // Changer la couleur du solde selon le montant
            lblTotalSolde.ForeColor = totalSolde >= 0 ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69);
        }

        private void UpdateButtonStates()
        {
            var hasSelection = dgvClients.SelectedRows.Count > 0;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
            btnViewAccount.Enabled = hasSelection;
        }

        private async void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            await FilterClientsAsync();
        }

        private async void CmbTypeClient_SelectedIndexChanged(object sender, EventArgs e)
        {
            await FilterClientsAsync();
        }

        private async void CmbStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            await FilterClientsAsync();
        }

        private async Task FilterClientsAsync()
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();
                var selectedType = cmbTypeClient.SelectedItem?.ToString();
                var selectedStatus = cmbStatus.SelectedItem?.ToString();

                var clients = await _clientRepository.GetAllAsync();
                
                // Filtrer par recherche
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    clients = clients.Where(c => 
                        c.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        c.Nom.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        (c.Prenom?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                        (c.RaisonSociale?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                        (c.Telephone?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
                }

                // Filtrer par type
                if (selectedType != "Tous")
                {
                    var typeClient = selectedType == "Particulier" ? TypeClient.Particulier : TypeClient.Entreprise;
                    clients = clients.Where(c => c.TypeClient == typeClient);
                }

                // Filtrer par statut
                if (selectedStatus != "Tous")
                {
                    var isActive = selectedStatus == "Actif";
                    clients = clients.Where(c => c.Actif == isActive);
                }

                dgvClients.DataSource = clients.ToList();
                UpdateStatistics(clients);
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors du filtrage : {ex.Message}");
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new FrmClientsAddEdit(_clientRepository, _notificationService);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadClientsAsync();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvClients.SelectedRows.Count == 0) return;

            var selectedClient = (Client)dgvClients.SelectedRows[0].DataBoundItem;
            var editForm = new FrmClientsAddEdit(_clientRepository, _notificationService, selectedClient);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                LoadClientsAsync();
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvClients.SelectedRows.Count == 0) return;

            var selectedClient = (Client)dgvClients.SelectedRows[0].DataBoundItem;
            
            if (_notificationService.ShowConfirmation("Confirmation", 
                $"Êtes-vous sûr de vouloir supprimer le client '{selectedClient.NomComplet}' ?"))
            {
                try
                {
                    await _clientRepository.DeleteAsync(selectedClient.Id);
                    _notificationService.ShowSuccess("Succès", "Client supprimé avec succès");
                    await LoadClientsAsync();
                }
                catch (Exception ex)
                {
                    _notificationService.ShowError("Erreur", $"Erreur lors de la suppression : {ex.Message}");
                }
            }
        }

        private void BtnViewAccount_Click(object sender, EventArgs e)
        {
            if (dgvClients.SelectedRows.Count == 0) return;

            var selectedClient = (Client)dgvClients.SelectedRows[0].DataBoundItem;
            
            // Ouvrir le formulaire de compte client
            // var accountForm = new FrmClientAccount(selectedClient, _clientRepository);
            // accountForm.ShowDialog();
            
            _notificationService.ShowInfo("Information", $"Ouverture du compte de {selectedClient.NomComplet}");
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadClientsAsync();
        }

        private void DgvClients_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private void DgvClients_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        private void DgvClients_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvClients.Columns[e.ColumnIndex].Name == "SoldeActuel" && e.Value != null)
            {
                var solde = Convert.ToDecimal(e.Value);
                if (solde < 0)
                {
                    e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69);
                    e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                }
                else if (solde > 0)
                {
                    e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69);
                }
            }

            if (dgvClients.Columns[e.ColumnIndex].Name == "TypeClient" && e.Value != null)
            {
                var type = e.Value.ToString();
                e.Value = type == "Particulier" ? "Particulier" : "Entreprise";
            }
        }
    }
}
