using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SupermarketManagement.Models;
using SupermarketManagement.Repositories;
using SupermarketManagement.Services;

namespace SupermarketManagement.Forms.Products
{
    /// <summary>
    /// Formulaire de liste des produits
    /// </summary>
    public partial class FrmProductsList : Form
    {
        private readonly IProduitRepository _produitRepository;
        private readonly INotificationService _notificationService;
        private DataGridView dgvProducts;
        private TextBox txtSearch;
        private Button btnAdd, btnEdit, btnDelete, btnRefresh;
        private ComboBox cmbCategory, cmbStatus;
        private Label lblTotal;

        public FrmProductsList(IProduitRepository produitRepository, INotificationService notificationService)
        {
            _produitRepository = produitRepository ?? throw new ArgumentNullException(nameof(produitRepository));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));

            InitializeComponent();
            InitializeFormControls();
            InitializeForm();
        }

        private void InitializeFormControls()
        {
            this.SuspendLayout();

            // Configuration du formulaire
            this.Text = "Gestion des Produits";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;

            CreateControls();
            ConfigureDataGridView();

            this.ResumeLayout(false);
        }

        private void InitializeForm()
        {
            this.Load += FrmProductsList_Load;
        }

        private void CreateControls()
        {
            // Panel supérieur pour les contrôles de recherche et actions
            var topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            // Recherche
            var lblSearch = new Label
            {
                Text = "Rechercher :",
                Location = new Point(10, 15),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 9)
            };

            txtSearch = new TextBox
            {
                Location = new Point(95, 12),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 9)
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;

            // Filtre par catégorie
            var lblCategory = new Label
            {
                Text = "Catégorie :",
                Location = new Point(310, 15),
                Size = new Size(70, 20),
                Font = new Font("Segoe UI", 9)
            };

            cmbCategory = new ComboBox
            {
                Location = new Point(385, 12),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9)
            };
            cmbCategory.SelectedIndexChanged += CmbCategory_SelectedIndexChanged;

            // Filtre par statut
            var lblStatus = new Label
            {
                Text = "Statut :",
                Location = new Point(550, 15),
                Size = new Size(50, 20),
                Font = new Font("Segoe UI", 9)
            };

            cmbStatus = new ComboBox
            {
                Location = new Point(605, 12),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9)
            };
            cmbStatus.Items.AddRange(new[] { "Tous", "Actif", "Inactif" });
            cmbStatus.SelectedIndex = 0;
            cmbStatus.SelectedIndexChanged += CmbStatus_SelectedIndexChanged;

            // Boutons d'action
            btnAdd = CreateButton("Ajouter", new Point(720, 10), Color.FromArgb(40, 167, 69));
            btnEdit = CreateButton("Modifier", new Point(820, 10), Color.FromArgb(0, 123, 255));
            btnDelete = CreateButton("Supprimer", new Point(920, 10), Color.FromArgb(220, 53, 69));
            btnRefresh = CreateButton("Actualiser", new Point(1020, 10), Color.FromArgb(108, 117, 125));

            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;

            // Label total
            lblTotal = new Label
            {
                Location = new Point(10, 50),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 122, 183)
            };

            topPanel.Controls.AddRange(new Control[]
            {
                lblSearch, txtSearch, lblCategory, cmbCategory, lblStatus, cmbStatus,
                btnAdd, btnEdit, btnDelete, btnRefresh, lblTotal
            });

            // Panel principal pour le DataGridView
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            dgvProducts = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 9)
            };

            mainPanel.Controls.Add(dgvProducts);

            this.Controls.Add(mainPanel);
            this.Controls.Add(topPanel);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(90, 30),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                UseVisualStyleBackColor = false
            };
        }

        private void ConfigureDataGridView()
        {
            dgvProducts.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Code",
                    HeaderText = "Code",
                    DataPropertyName = "Code",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Nom",
                    HeaderText = "Nom du Produit",
                    DataPropertyName = "Nom",
                    Width = 200,
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Categorie",
                    HeaderText = "Catégorie",
                    DataPropertyName = "CategorieName",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "PrixAchat",
                    HeaderText = "Prix Achat",
                    DataPropertyName = "PrixAchatUnitaire",
                    Width = 100,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "PrixVente",
                    HeaderText = "Prix Vente",
                    DataPropertyName = "PrixVenteUnitaire",
                    Width = 100,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Stock",
                    HeaderText = "Stock",
                    DataPropertyName = "StockActuel",
                    Width = 80,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "StatutStock",
                    HeaderText = "Statut Stock",
                    DataPropertyName = "StatutStock",
                    Width = 100
                },
                new DataGridViewCheckBoxColumn
                {
                    Name = "Actif",
                    HeaderText = "Actif",
                    DataPropertyName = "Actif",
                    Width = 60
                }
            });

            // Style des en-têtes
            dgvProducts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(51, 122, 183);
            dgvProducts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvProducts.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9, FontStyle.Bold);

            // Style des lignes alternées
            dgvProducts.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // Événements
            dgvProducts.CellDoubleClick += DgvProducts_CellDoubleClick;
            dgvProducts.SelectionChanged += DgvProducts_SelectionChanged;
            dgvProducts.CellFormatting += DgvProducts_CellFormatting;
        }

        private async void FrmProductsList_Load(object sender, EventArgs e)
        {
            await LoadCategoriesAsync();
            await LoadProductsAsync();
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                // Charger les catégories depuis le repository
                // var categories = await _categorieRepository.GetActiveAsync();
                cmbCategory.Items.Clear();
                cmbCategory.Items.Add("Toutes");
                // cmbCategory.Items.AddRange(categories.Select(c => c.Nom).ToArray());
                cmbCategory.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors du chargement des catégories : {ex.Message}");
            }
        }

        private async Task LoadProductsAsync()
        {
            try
            {
                var products = await _produitRepository.GetAllAsync();
                dgvProducts.DataSource = products.ToList();
                lblTotal.Text = $"Total : {products.Count()} produits";
                
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors du chargement des produits : {ex.Message}");
            }
        }

        private void UpdateButtonStates()
        {
            var hasSelection = dgvProducts.SelectedRows.Count > 0;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
        }

        private async void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            await FilterProductsAsync();
        }

        private async void CmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            await FilterProductsAsync();
        }

        private async void CmbStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            await FilterProductsAsync();
        }

        private async Task FilterProductsAsync()
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();
                var selectedCategory = cmbCategory.SelectedItem?.ToString();
                var selectedStatus = cmbStatus.SelectedItem?.ToString();

                // Implémentation du filtrage
                var products = await _produitRepository.GetAllAsync();
                
                // Filtrer par recherche
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    products = products.Where(p => 
                        p.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        p.Nom.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
                }

                // Filtrer par statut
                if (selectedStatus != "Tous")
                {
                    var isActive = selectedStatus == "Actif";
                    products = products.Where(p => p.Actif == isActive);
                }

                dgvProducts.DataSource = products.ToList();
                lblTotal.Text = $"Total : {products.Count()} produits";
            }
            catch (Exception ex)
            {
                _notificationService.ShowError("Erreur", $"Erreur lors du filtrage : {ex.Message}");
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new FrmProductsAddEdit(_produitRepository, _notificationService);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadProductsAsync();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count == 0) return;

            var selectedProduct = (Produit)dgvProducts.SelectedRows[0].DataBoundItem;
            var editForm = new FrmProductsAddEdit(_produitRepository, _notificationService, selectedProduct);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                LoadProductsAsync();
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count == 0) return;

            var selectedProduct = (Produit)dgvProducts.SelectedRows[0].DataBoundItem;
            
            if (_notificationService.ShowConfirmation("Confirmation", 
                $"Êtes-vous sûr de vouloir supprimer le produit '{selectedProduct.Nom}' ?"))
            {
                try
                {
                    await _produitRepository.DeleteAsync(selectedProduct.Id);
                    _notificationService.ShowSuccess("Succès", "Produit supprimé avec succès");
                    await LoadProductsAsync();
                }
                catch (Exception ex)
                {
                    _notificationService.ShowError("Erreur", $"Erreur lors de la suppression : {ex.Message}");
                }
            }
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadProductsAsync();
        }

        private void DgvProducts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private void DgvProducts_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        private void DgvProducts_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvProducts.Columns[e.ColumnIndex].Name == "StatutStock" && e.Value != null)
            {
                var statut = e.Value.ToString();
                switch (statut)
                {
                    case "Rupture":
                        e.CellStyle.BackColor = Color.FromArgb(248, 215, 218);
                        e.CellStyle.ForeColor = Color.FromArgb(114, 28, 36);
                        break;
                    case "Alerte":
                        e.CellStyle.BackColor = Color.FromArgb(255, 243, 205);
                        e.CellStyle.ForeColor = Color.FromArgb(133, 100, 4);
                        break;
                    case "Normal":
                        e.CellStyle.BackColor = Color.FromArgb(212, 237, 218);
                        e.CellStyle.ForeColor = Color.FromArgb(21, 87, 36);
                        break;
                }
            }
        }
    }
}
