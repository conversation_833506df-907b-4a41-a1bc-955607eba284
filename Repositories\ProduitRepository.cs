using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using SupermarketManagement.Data;
using SupermarketManagement.Models;

namespace SupermarketManagement.Repositories
{
    /// <summary>
    /// Interface pour le repository des produits
    /// </summary>
    public interface IProduitRepository : IActivatableRepository<Produit>
    {
        /// <summary>
        /// Obtient un produit par son code
        /// </summary>
        /// <param name="codeProduit">Code du produit</param>
        /// <returns>Produit ou null</returns>
        Task<Produit> GetByCodeAsync(string codeProduit);

        /// <summary>
        /// Obtient un produit par code-barres
        /// </summary>
        /// <param name="codeBarre">Code-barres</param>
        /// <returns>Produit ou null</returns>
        Task<Produit> GetByBarcodeAsync(string codeBarre);

        /// <summary>
        /// Obtient les produits d'une catégorie
        /// </summary>
        /// <param name="categorieId">ID de la catégorie</param>
        /// <returns>Liste des produits</returns>
        Task<IEnumerable<Produit>> GetByCategorieAsync(int categorieId);

        /// <summary>
        /// Obtient les produits en rupture de stock
        /// </summary>
        /// <returns>Liste des produits en rupture</returns>
        Task<IEnumerable<Produit>> GetOutOfStockAsync();

        /// <summary>
        /// Obtient les produits en alerte de stock
        /// </summary>
        /// <returns>Liste des produits en alerte</returns>
        Task<IEnumerable<Produit>> GetLowStockAsync();

        /// <summary>
        /// Met à jour le stock d'un produit
        /// </summary>
        /// <param name="produitId">ID du produit</param>
        /// <param name="quantite">Quantité à ajouter/retirer</param>
        /// <param name="isAddition">True pour ajouter, False pour retirer</param>
        /// <returns>True si la mise à jour réussit</returns>
        Task<bool> UpdateStockAsync(int produitId, decimal quantite, bool isAddition = true);

        /// <summary>
        /// Met à jour le PMP d'un produit
        /// </summary>
        /// <param name="produitId">ID du produit</param>
        /// <param name="nouveauPMP">Nouveau PMP</param>
        /// <returns>True si la mise à jour réussit</returns>
        Task<bool> UpdatePMPAsync(int produitId, decimal nouveauPMP);

        /// <summary>
        /// Vérifie si un code produit existe déjà
        /// </summary>
        /// <param name="codeProduit">Code à vérifier</param>
        /// <param name="excludeId">ID à exclure de la vérification</param>
        /// <returns>True si le code existe</returns>
        Task<bool> CodeExistsAsync(string codeProduit, int? excludeId = null);

        /// <summary>
        /// Génère le prochain code produit automatique
        /// </summary>
        /// <returns>Nouveau code produit</returns>
        Task<string> GenerateNextCodeAsync();

        /// <summary>
        /// Obtient les codes-barres d'un produit
        /// </summary>
        /// <param name="produitId">ID du produit</param>
        /// <returns>Liste des codes-barres</returns>
        Task<IEnumerable<CodeBarre>> GetCodesBarresAsync(int produitId);

        /// <summary>
        /// Ajoute un code-barres à un produit
        /// </summary>
        /// <param name="codeBarre">Code-barres à ajouter</param>
        /// <returns>ID du code-barres créé</returns>
        Task<int> AddCodeBarreAsync(CodeBarre codeBarre);

        /// <summary>
        /// Supprime un code-barres
        /// </summary>
        /// <param name="codeBarreId">ID du code-barres</param>
        /// <returns>True si la suppression réussit</returns>
        Task<bool> DeleteCodeBarreAsync(int codeBarreId);
    }

    /// <summary>
    /// Repository pour la gestion des produits
    /// </summary>
    public class ProduitRepository : ActivatableRepository<Produit>, IProduitRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        public ProduitRepository(DapperContext context) : base(context, "produits")
        {
        }

        /// <summary>
        /// Obtient un produit par son code
        /// </summary>
        public async Task<Produit> GetByCodeAsync(string codeProduit)
        {
            var sql = @"
                SELECT p.*, c.nom as CategorieName, u.nom as UniteBaseName
                FROM produits p
                LEFT JOIN categories c ON p.categorie_id = c.id
                LEFT JOIN unites_mesure u ON p.unite_base_id = u.id
                WHERE p.code_produit = @CodeProduit";

            return await _context.QueryFirstOrDefaultAsync<Produit>(sql, new { CodeProduit = codeProduit });
        }

        /// <summary>
        /// Obtient un produit par code-barres
        /// </summary>
        public async Task<Produit> GetByBarcodeAsync(string codeBarre)
        {
            var sql = @"
                SELECT p.*, c.nom as CategorieName, u.nom as UniteBaseName
                FROM produits p
                LEFT JOIN categories c ON p.categorie_id = c.id
                LEFT JOIN unites_mesure u ON p.unite_base_id = u.id
                INNER JOIN codes_barres cb ON p.id = cb.produit_id
                WHERE cb.code_barre = @CodeBarre AND cb.actif = 1 AND p.actif = 1";

            return await _context.QueryFirstOrDefaultAsync<Produit>(sql, new { CodeBarre = codeBarre });
        }

        /// <summary>
        /// Obtient les produits d'une catégorie
        /// </summary>
        public async Task<IEnumerable<Produit>> GetByCategorieAsync(int categorieId)
        {
            var sql = @"
                SELECT p.*, c.nom as CategorieName, u.nom as UniteBaseName
                FROM produits p
                LEFT JOIN categories c ON p.categorie_id = c.id
                LEFT JOIN unites_mesure u ON p.unite_base_id = u.id
                WHERE p.categorie_id = @CategorieId AND p.actif = 1
                ORDER BY p.nom";

            return await _context.QueryAsync<Produit>(sql, new { CategorieId = categorieId });
        }

        /// <summary>
        /// Obtient les produits en rupture de stock
        /// </summary>
        public async Task<IEnumerable<Produit>> GetOutOfStockAsync()
        {
            var sql = @"
                SELECT p.*, c.nom as CategorieName, u.nom as UniteBaseName
                FROM produits p
                LEFT JOIN categories c ON p.categorie_id = c.id
                LEFT JOIN unites_mesure u ON p.unite_base_id = u.id
                WHERE p.stock_actuel <= p.stock_min AND p.actif = 1
                ORDER BY p.stock_actuel ASC";

            return await _context.QueryAsync<Produit>(sql);
        }

        /// <summary>
        /// Obtient les produits en alerte de stock
        /// </summary>
        public async Task<IEnumerable<Produit>> GetLowStockAsync()
        {
            var sql = @"
                SELECT p.*, c.nom as CategorieName, u.nom as UniteBaseName
                FROM produits p
                LEFT JOIN categories c ON p.categorie_id = c.id
                LEFT JOIN unites_mesure u ON p.unite_base_id = u.id
                WHERE p.stock_actuel <= (p.stock_min * 1.2) AND p.stock_actuel > p.stock_min AND p.actif = 1
                ORDER BY p.stock_actuel ASC";

            return await _context.QueryAsync<Produit>(sql);
        }

        /// <summary>
        /// Met à jour le stock d'un produit
        /// </summary>
        public async Task<bool> UpdateStockAsync(int produitId, decimal quantite, bool isAddition = true)
        {
            var operation = isAddition ? "+" : "-";
            var sql = $@"
                UPDATE produits 
                SET stock_actuel = stock_actuel {operation} @Quantite,
                    date_modification = @DateModification
                WHERE id = @ProduitId";

            var affectedRows = await _context.ExecuteAsync(sql, new 
            { 
                ProduitId = produitId, 
                Quantite = quantite,
                DateModification = DateTime.Now
            });

            return affectedRows > 0;
        }

        /// <summary>
        /// Met à jour le PMP d'un produit
        /// </summary>
        public async Task<bool> UpdatePMPAsync(int produitId, decimal nouveauPMP)
        {
            var sql = @"
                UPDATE produits 
                SET pmp = @NouveauPMP,
                    date_modification = @DateModification
                WHERE id = @ProduitId";

            var affectedRows = await _context.ExecuteAsync(sql, new 
            { 
                ProduitId = produitId, 
                NouveauPMP = nouveauPMP,
                DateModification = DateTime.Now
            });

            return affectedRows > 0;
        }

        /// <summary>
        /// Vérifie si un code produit existe déjà
        /// </summary>
        public async Task<bool> CodeExistsAsync(string codeProduit, int? excludeId = null)
        {
            var sql = "SELECT COUNT(1) FROM produits WHERE code_produit = @CodeProduit";
            var parameters = new { CodeProduit = codeProduit };

            if (excludeId.HasValue)
            {
                sql += " AND id != @ExcludeId";
                parameters = new { CodeProduit = codeProduit, ExcludeId = excludeId.Value };
            }

            var count = await _context.ExecuteScalarAsync<int>(sql, parameters);
            return count > 0;
        }

        /// <summary>
        /// Génère le prochain code produit automatique
        /// </summary>
        public async Task<string> GenerateNextCodeAsync()
        {
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTRING(code_produit, 3) AS UNSIGNED)), 0) + 1 
                FROM produits 
                WHERE code_produit REGEXP '^PR[0-9]+$'";

            var nextNumber = await _context.ExecuteScalarAsync<int>(sql);
            return $"PR{nextNumber:D6}"; // Format: PR000001
        }

        /// <summary>
        /// Obtient les codes-barres d'un produit
        /// </summary>
        public async Task<IEnumerable<CodeBarre>> GetCodesBarresAsync(int produitId)
        {
            var sql = @"
                SELECT cb.*, u.nom as UniteNom, u.symbole as UniteSymbole
                FROM codes_barres cb
                LEFT JOIN unites_mesure u ON cb.unite_id = u.id
                WHERE cb.produit_id = @ProduitId AND cb.actif = 1
                ORDER BY cb.principal DESC, cb.id";

            return await _context.QueryAsync<CodeBarre>(sql, new { ProduitId = produitId });
        }

        /// <summary>
        /// Ajoute un code-barres à un produit
        /// </summary>
        public async Task<int> AddCodeBarreAsync(CodeBarre codeBarre)
        {
            var sql = @"
                INSERT INTO codes_barres (
                    produit_id, code_barre, unite_id, quantite_unite, principal, actif, date_creation
                ) VALUES (
                    @ProduitId, @Code, @UniteId, @QuantiteUnite, @Principal, @Actif, @DateCreation
                );
                SELECT LAST_INSERT_ID();";

            codeBarre.DateCreation = DateTime.Now;
            return await _context.ExecuteScalarAsync<int>(sql, codeBarre);
        }

        /// <summary>
        /// Supprime un code-barres
        /// </summary>
        public async Task<bool> DeleteCodeBarreAsync(int codeBarreId)
        {
            var sql = "DELETE FROM codes_barres WHERE id = @Id";
            var affectedRows = await _context.ExecuteAsync(sql, new { Id = codeBarreId });
            return affectedRows > 0;
        }

        /// <summary>
        /// Génère le SQL d'insertion
        /// </summary>
        protected override string GenerateInsertSql()
        {
            return @"
                INSERT INTO produits (
                    code_produit, nom, description, categorie_id, unite_base_id, prix_achat_unitaire,
                    prix_vente_unitaire, prix_vente_ht, taux_tva, prix_fardeau, pieces_par_fardeau,
                    stock_min, stock_max, stock_actuel, stock_reserve, pmp, image_path, actif,
                    date_creation, date_modification
                ) VALUES (
                    @Code, @Nom, @Description, @CategorieId, @UniteBaseId, @PrixAchatUnitaire,
                    @PrixVenteUnitaire, @PrixVenteHT, @TauxTVA, @PrixFardeau, @PiecesParFardeau,
                    @StockMin, @StockMax, @StockActuel, @StockReserve, @PMP, @ImagePath, @Actif,
                    @DateCreation, @DateModification
                );
                SELECT LAST_INSERT_ID();";
        }

        /// <summary>
        /// Génère le SQL de mise à jour
        /// </summary>
        protected override string GenerateUpdateSql()
        {
            return @"
                UPDATE produits SET 
                    code_produit = @Code,
                    nom = @Nom,
                    description = @Description,
                    categorie_id = @CategorieId,
                    unite_base_id = @UniteBaseId,
                    prix_achat_unitaire = @PrixAchatUnitaire,
                    prix_vente_unitaire = @PrixVenteUnitaire,
                    prix_vente_ht = @PrixVenteHT,
                    taux_tva = @TauxTVA,
                    prix_fardeau = @PrixFardeau,
                    pieces_par_fardeau = @PiecesParFardeau,
                    stock_min = @StockMin,
                    stock_max = @StockMax,
                    stock_actuel = @StockActuel,
                    stock_reserve = @StockReserve,
                    pmp = @PMP,
                    image_path = @ImagePath,
                    actif = @Actif,
                    date_modification = @DateModification
                WHERE id = @Id";
        }

        /// <summary>
        /// Obtient les colonnes de recherche
        /// </summary>
        protected override string[] GetSearchColumns()
        {
            return new[] { "code_produit", "nom", "description" };
        }
    }
}
