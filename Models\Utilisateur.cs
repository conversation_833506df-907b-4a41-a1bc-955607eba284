using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Énumération des rôles utilisateur
    /// </summary>
    public enum RoleUtilisateur
    {
        Ad<PERSON>,
        Vendeur,
        Gestionnaire,
        Comptable
    }

    /// <summary>
    /// Modèle pour les utilisateurs du système
    /// </summary>
    [Table("utilisateurs")]
    public class Utilisateur : BaseEntity, IActivatable
    {
        [Required]
        [StringLength(100)]
        [Column("nom_utilisateur")]
        public string NomUtilisateur { get; set; }

        [Required]
        [StringLength(255)]
        [Column("mot_de_passe")]
        public string MotDePasse { get; set; }

        [Required]
        [StringLength(255)]
        [Column("nom_complet")]
        public string NomComplet { get; set; }

        [StringLength(100)]
        [Column("email")]
        public string Email { get; set; }

        [StringLength(50)]
        [Column("telephone")]
        public string Telephone { get; set; }

        [Required]
        [Column("role")]
        public RoleUtilisateur Role { get; set; }

        [Column("permissions")]
        public string Permissions { get; set; } // JSON string

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("derniere_connexion")]
        public DateTime? DerniereConnexion { get; set; }

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        [Column("date_modification")]
        public new DateTime DateModification { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Modèle pour les sessions utilisateur
    /// </summary>
    [Table("sessions_utilisateurs")]
    public class SessionUtilisateur : BaseEntity
    {
        [Required]
        [Column("utilisateur_id")]
        public int UtilisateurId { get; set; }

        [Column("date_connexion")]
        public DateTime DateConnexion { get; set; } = DateTime.Now;

        [Column("date_deconnexion")]
        public DateTime? DateDeconnexion { get; set; }

        [StringLength(45)]
        [Column("adresse_ip")]
        public string AdresseIP { get; set; }

        [Column("statut")]
        public string Statut { get; set; } = "active"; // active, fermee

        // Navigation property
        public virtual Utilisateur Utilisateur { get; set; }
    }
}
