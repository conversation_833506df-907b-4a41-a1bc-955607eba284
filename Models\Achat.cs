using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Énumération des modes de paiement pour les achats
    /// </summary>
    public enum ModePaiementAchat
    {
        Especes,
        Cheque,
        Virement,
        Credit
    }

    /// <summary>
    /// Modèle pour les factures d'achat
    /// </summary>
    [Table("factures_achat")]
    public class FactureAchat : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Column("numero_facture")]
        public string NumeroFacture { get; set; }

        [StringLength(100)]
        [Column("numero_facture_fournisseur")]
        public string NumeroFactureFournisseur { get; set; }

        [Required]
        [Column("fournisseur_id")]
        public int FournisseurId { get; set; }

        [Required]
        [Column("utilisateur_id")]
        public int UtilisateurId { get; set; }

        [Required]
        [Column("date_facture")]
        public DateTime DateFacture { get; set; } = DateTime.Today;

        [Column("date_echeance")]
        public DateTime? DateEcheance { get; set; }

        [Required]
        [Column("sous_total_ht")]
        public decimal SousTotalHT { get; set; } = 0.00m;

        [Column("total_remise")]
        public decimal TotalRemise { get; set; } = 0.00m;

        [Required]
        [Column("total_tva")]
        public decimal TotalTVA { get; set; } = 0.00m;

        [Required]
        [Column("total_ttc")]
        public decimal TotalTTC { get; set; } = 0.00m;

        [Column("montant_paye")]
        public decimal MontantPaye { get; set; } = 0.00m;

        [Column("statut")]
        public StatutFacture Statut { get; set; } = StatutFacture.Brouillon;

        [Column("mode_paiement")]
        public ModePaiementAchat ModePaiement { get; set; } = ModePaiementAchat.Credit;

        [Column("notes")]
        public string Notes { get; set; }

        [Column("caisse_id")]
        public int? CaisseId { get; set; }

        [Column("banque_id")]
        public int? BanqueId { get; set; }

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        [Column("date_modification")]
        public new DateTime DateModification { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Fournisseur Fournisseur { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual Caisse Caisse { get; set; }
        public virtual Banque Banque { get; set; }
        public virtual ICollection<DetailFactureAchat> Details { get; set; } = new List<DetailFactureAchat>();

        // Propriétés calculées
        [NotMapped]
        public decimal MontantRestant => TotalTTC - MontantPaye;

        [NotMapped]
        public bool EstPayee => MontantRestant <= 0;

        [NotMapped]
        public int NombreArticles => Details?.Sum(d => (int)d.Quantite) ?? 0;

        [NotMapped]
        public string StatutPaiement
        {
            get
            {
                if (MontantPaye == 0) return "Non payée";
                if (MontantRestant <= 0) return "Payée";
                return "Partiellement payée";
            }
        }

        [NotMapped]
        public bool EstEnRetard => DateEcheance.HasValue && DateEcheance.Value < DateTime.Today && MontantRestant > 0;
    }

    /// <summary>
    /// Modèle pour les détails de facture d'achat
    /// </summary>
    [Table("details_facture_achat")]
    public class DetailFactureAchat : BaseEntity
    {
        [Required]
        [Column("facture_id")]
        public int FactureId { get; set; }

        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Required]
        [Column("unite_id")]
        public int UniteId { get; set; }

        [Required]
        [Column("quantite")]
        public decimal Quantite { get; set; }

        [Required]
        [Column("prix_unitaire_ht")]
        public decimal PrixUnitaireHT { get; set; }

        [Required]
        [Column("prix_unitaire_ttc")]
        public decimal PrixUnitaireTTC { get; set; }

        [Column("remise_pourcentage")]
        public decimal RemisePourcentage { get; set; } = 0.00m;

        [Column("remise_montant")]
        public decimal RemiseMontant { get; set; } = 0.00m;

        [Required]
        [Column("taux_tva")]
        public decimal TauxTVA { get; set; }

        [Required]
        [Column("montant_tva")]
        public decimal MontantTVA { get; set; }

        [Required]
        [Column("sous_total_ht")]
        public decimal SousTotalHT { get; set; }

        [Required]
        [Column("sous_total_ttc")]
        public decimal SousTotalTTC { get; set; }

        // Navigation properties
        public virtual FactureAchat Facture { get; set; }
        public virtual Produit Produit { get; set; }
        public virtual UniteMesure Unite { get; set; }

        // Propriétés calculées
        [NotMapped]
        public decimal PrixUnitaireApresRemise => PrixUnitaireHT - (PrixUnitaireHT * RemisePourcentage / 100) - RemiseMontant;

        [NotMapped]
        public decimal TotalRemiseLigne => (PrixUnitaireHT * RemisePourcentage / 100 * Quantite) + RemiseMontant;
    }

    /// <summary>
    /// Modèle pour les retours fournisseurs
    /// </summary>
    [Table("retours_fournisseur")]
    public class RetourFournisseur : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Column("numero_retour")]
        public string NumeroRetour { get; set; }

        [Column("facture_achat_id")]
        public int? FactureAchatId { get; set; }

        [Required]
        [Column("fournisseur_id")]
        public int FournisseurId { get; set; }

        [Required]
        [Column("utilisateur_id")]
        public int UtilisateurId { get; set; }

        [Required]
        [Column("date_retour")]
        public DateTime DateRetour { get; set; } = DateTime.Today;

        [Column("motif")]
        public string Motif { get; set; }

        [Required]
        [Column("total_retour")]
        public decimal TotalRetour { get; set; } = 0.00m;

        [Column("statut")]
        public StatutRetour Statut { get; set; } = StatutRetour.EnAttente;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual FactureAchat FactureAchat { get; set; }
        public virtual Fournisseur Fournisseur { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual ICollection<DetailRetourFournisseur> Details { get; set; } = new List<DetailRetourFournisseur>();
    }

    /// <summary>
    /// Modèle pour les détails de retour fournisseur
    /// </summary>
    [Table("details_retour_fournisseur")]
    public class DetailRetourFournisseur : BaseEntity
    {
        [Required]
        [Column("retour_id")]
        public int RetourId { get; set; }

        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Required]
        [Column("unite_id")]
        public int UniteId { get; set; }

        [Required]
        [Column("quantite_retournee")]
        public decimal QuantiteRetournee { get; set; }

        [Required]
        [Column("prix_unitaire")]
        public decimal PrixUnitaire { get; set; }

        [Required]
        [Column("sous_total")]
        public decimal SousTotal { get; set; }

        // Navigation properties
        public virtual RetourFournisseur Retour { get; set; }
        public virtual Produit Produit { get; set; }
        public virtual UniteMesure Unite { get; set; }
    }
}
