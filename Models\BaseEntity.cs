using System;
using System.ComponentModel.DataAnnotations;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Classe de base pour toutes les entités avec propriétés communes
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }
        
        public DateTime DateCreation { get; set; } = DateTime.Now;
        
        public DateTime DateModification { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Interface pour les entités qui peuvent être activées/désactivées
    /// </summary>
    public interface IActivatable
    {
        bool Actif { get; set; }
    }

    /// <summary>
    /// Interface pour les entités avec code unique
    /// </summary>
    public interface ICodedEntity
    {
        string Code { get; set; }
    }

    /// <summary>
    /// Interface pour les entités avec nom
    /// </summary>
    public interface INamedEntity
    {
        string Nom { get; set; }
    }
}
