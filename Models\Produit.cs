using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Énumération des types d'unité de mesure
    /// </summary>
    public enum TypeUniteMesure
    {
        Poids,
        Volume,
        Longueur,
        Piece
    }

    /// <summary>
    /// Modèle pour les unités de mesure
    /// </summary>
    [Table("unites_mesure")]
    public class UniteMesure : BaseEntity, IActivatable, INamedEntity
    {
        [Required]
        [StringLength(100)]
        [Column("nom")]
        public string Nom { get; set; }

        [Required]
        [StringLength(20)]
        [Column("symbole")]
        public string Symbole { get; set; }

        [Required]
        [Column("type")]
        public TypeUniteMesure Type { get; set; }

        [Column("actif")]
        public bool Actif { get; set; } = true;
    }

    /// <summary>
    /// Modèle pour les catégories de produits
    /// </summary>
    [Table("categories")]
    public class Categorie : BaseEntity, IActivatable, INamedEntity
    {
        [Required]
        [StringLength(255)]
        [Column("nom")]
        public string Nom { get; set; }

        [Column("description")]
        public string Description { get; set; }

        [Column("parent_id")]
        public int? ParentId { get; set; }

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Categorie Parent { get; set; }
        public virtual ICollection<Categorie> SousCategories { get; set; }
        public virtual ICollection<Produit> Produits { get; set; }
    }

    /// <summary>
    /// Modèle pour les produits
    /// </summary>
    [Table("produits")]
    public class Produit : BaseEntity, IActivatable, ICodedEntity, INamedEntity
    {
        [Required]
        [StringLength(100)]
        [Column("code_produit")]
        public string Code { get; set; }

        [Required]
        [StringLength(255)]
        [Column("nom")]
        public string Nom { get; set; }

        [Column("description")]
        public string Description { get; set; }

        [Required]
        [Column("categorie_id")]
        public int CategorieId { get; set; }

        [Required]
        [Column("unite_base_id")]
        public int UniteBaseId { get; set; }

        [Required]
        [Column("prix_achat_unitaire")]
        public decimal PrixAchatUnitaire { get; set; }

        [Required]
        [Column("prix_vente_unitaire")]
        public decimal PrixVenteUnitaire { get; set; }

        [Required]
        [Column("prix_vente_ht")]
        public decimal PrixVenteHT { get; set; }

        [Column("taux_tva")]
        public decimal TauxTVA { get; set; } = 19.00m;

        [Column("prix_fardeau")]
        public decimal? PrixFardeau { get; set; }

        [Column("pieces_par_fardeau")]
        public int PiecesParFardeau { get; set; } = 1;

        [Column("stock_min")]
        public decimal StockMin { get; set; } = 0;

        [Column("stock_max")]
        public decimal StockMax { get; set; } = 0;

        [Column("stock_actuel")]
        public decimal StockActuel { get; set; } = 0;

        [Column("stock_reserve")]
        public decimal StockReserve { get; set; } = 0;

        [Column("pmp")]
        public decimal PMP { get; set; } = 0.00m; // Prix Moyen Pondéré

        [StringLength(500)]
        [Column("image_path")]
        public string ImagePath { get; set; }

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        [Column("date_modification")]
        public new DateTime DateModification { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Categorie Categorie { get; set; }
        public virtual UniteMesure UniteBase { get; set; }
        public virtual ICollection<CodeBarre> CodesBarres { get; set; }
        public virtual ICollection<UniteVenteProduit> UnitesVente { get; set; }

        // Propriétés calculées
        [NotMapped]
        public decimal StockDisponible => StockActuel - StockReserve;

        [NotMapped]
        public string StatutStock
        {
            get
            {
                if (StockActuel <= StockMin) return "Rupture";
                if (StockActuel <= StockMin * 1.2m) return "Alerte";
                return "Normal";
            }
        }

        [NotMapped]
        public decimal ValeurStock => StockActuel * PrixAchatUnitaire;

        [NotMapped]
        public decimal MargeUnitaire => PrixVenteUnitaire - PrixAchatUnitaire;

        [NotMapped]
        public decimal PourcentageMarge => PrixAchatUnitaire > 0 ? (MargeUnitaire / PrixAchatUnitaire) * 100 : 0;
    }

    /// <summary>
    /// Modèle pour les codes-barres multiples d'un produit
    /// </summary>
    [Table("codes_barres")]
    public class CodeBarre : BaseEntity, IActivatable
    {
        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Required]
        [StringLength(255)]
        [Column("code_barre")]
        public string Code { get; set; }

        [Required]
        [Column("unite_id")]
        public int UniteId { get; set; }

        [Column("quantite_unite")]
        public decimal QuantiteUnite { get; set; } = 1.00m;

        [Column("principal")]
        public bool Principal { get; set; } = false;

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Produit Produit { get; set; }
        public virtual UniteMesure Unite { get; set; }
    }

    /// <summary>
    /// Modèle pour les unités de vente multiples d'un produit
    /// </summary>
    [Table("unites_vente_produit")]
    public class UniteVenteProduit : BaseEntity, IActivatable
    {
        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Required]
        [Column("unite_id")]
        public int UniteId { get; set; }

        [Required]
        [Column("facteur_conversion")]
        public decimal FacteurConversion { get; set; } = 1.0000m;

        [Required]
        [Column("prix_vente")]
        public decimal PrixVente { get; set; }

        [StringLength(255)]
        [Column("code_barre")]
        public string CodeBarre { get; set; }

        [Column("actif")]
        public bool Actif { get; set; } = true;

        // Navigation properties
        public virtual Produit Produit { get; set; }
        public virtual UniteMesure Unite { get; set; }
    }
}
