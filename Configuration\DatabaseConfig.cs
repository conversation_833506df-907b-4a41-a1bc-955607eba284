using System;
using System.Configuration;

namespace SupermarketManagement.Configuration
{
    /// <summary>
    /// Configuration de la base de données
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// Chaîne de connexion MySQL
        /// </summary>
        public static string ConnectionString
        {
            get
            {
                var connectionString = ConfigurationManager.ConnectionStrings["SupermarketDB"]?.ConnectionString;
                
                if (string.IsNullOrEmpty(connectionString))
                {
                    // Configuration par défaut si aucune chaîne de connexion n'est trouvée
                    connectionString = BuildDefaultConnectionString();
                }
                
                return connectionString;
            }
        }

        /// <summary>
        /// Serveur de base de données
        /// </summary>
        public static string Server => ConfigurationManager.AppSettings["DB_Server"] ?? "localhost";

        /// <summary>
        /// Port de la base de données
        /// </summary>
        public static string Port => ConfigurationManager.AppSettings["DB_Port"] ?? "3306";

        /// <summary>
        /// Nom de la base de données
        /// </summary>
        public static string Database => ConfigurationManager.AppSettings["DB_Database"] ?? "supermarket_db";

        /// <summary>
        /// Nom d'utilisateur
        /// </summary>
        public static string Username => ConfigurationManager.AppSettings["DB_Username"] ?? "root";

        /// <summary>
        /// Mot de passe
        /// </summary>
        public static string Password => ConfigurationManager.AppSettings["DB_Password"] ?? "";

        /// <summary>
        /// Timeout de connexion en secondes
        /// </summary>
        public static int ConnectionTimeout
        {
            get
            {
                if (int.TryParse(ConfigurationManager.AppSettings["DB_ConnectionTimeout"], out int timeout))
                    return timeout;
                return 30; // Valeur par défaut
            }
        }

        /// <summary>
        /// Timeout de commande en secondes
        /// </summary>
        public static int CommandTimeout
        {
            get
            {
                if (int.TryParse(ConfigurationManager.AppSettings["DB_CommandTimeout"], out int timeout))
                    return timeout;
                return 60; // Valeur par défaut
            }
        }

        /// <summary>
        /// Construit la chaîne de connexion par défaut
        /// </summary>
        /// <returns>Chaîne de connexion MySQL</returns>
        private static string BuildDefaultConnectionString()
        {
            return $"Server={Server};Port={Port};Database={Database};Uid={Username};Pwd={Password};" +
                   $"Connection Timeout={ConnectionTimeout};Command Timeout={CommandTimeout};" +
                   "CharSet=utf8mb4;SslMode=None;AllowUserVariables=True;";
        }

        /// <summary>
        /// Construit une chaîne de connexion personnalisée
        /// </summary>
        /// <param name="server">Serveur</param>
        /// <param name="port">Port</param>
        /// <param name="database">Base de données</param>
        /// <param name="username">Nom d'utilisateur</param>
        /// <param name="password">Mot de passe</param>
        /// <returns>Chaîne de connexion</returns>
        public static string BuildConnectionString(string server, string port, string database, 
            string username, string password)
        {
            return $"Server={server};Port={port};Database={database};Uid={username};Pwd={password};" +
                   $"Connection Timeout={ConnectionTimeout};Command Timeout={CommandTimeout};" +
                   "CharSet=utf8mb4;SslMode=None;AllowUserVariables=True;";
        }

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        /// <param name="connectionString">Chaîne de connexion à tester</param>
        /// <returns>True si la connexion réussit</returns>
        public static bool TestConnection(string connectionString = null)
        {
            try
            {
                using var connection = new MySql.Data.MySqlClient.MySqlConnection(connectionString ?? ConnectionString);
                connection.Open();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Configuration de l'application
    /// </summary>
    public static class AppConfig
    {
        /// <summary>
        /// Nom de l'application
        /// </summary>
        public static string ApplicationName => ConfigurationManager.AppSettings["ApplicationName"] ?? "Gestion Supermarché";

        /// <summary>
        /// Version de l'application
        /// </summary>
        public static string Version => ConfigurationManager.AppSettings["Version"] ?? "1.0.0";

        /// <summary>
        /// Langue par défaut
        /// </summary>
        public static string DefaultLanguage => ConfigurationManager.AppSettings["DefaultLanguage"] ?? "fr-DZ";

        /// <summary>
        /// Devise par défaut
        /// </summary>
        public static string DefaultCurrency => ConfigurationManager.AppSettings["DefaultCurrency"] ?? "DZD";

        /// <summary>
        /// Taux de TVA par défaut
        /// </summary>
        public static decimal DefaultTVARate
        {
            get
            {
                if (decimal.TryParse(ConfigurationManager.AppSettings["DefaultTVARate"], out decimal rate))
                    return rate;
                return 19.00m; // Valeur par défaut pour l'Algérie
            }
        }

        /// <summary>
        /// Répertoire des sauvegardes
        /// </summary>
        public static string BackupDirectory => ConfigurationManager.AppSettings["BackupDirectory"] ?? 
            System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SupermarketBackups");

        /// <summary>
        /// Répertoire des rapports
        /// </summary>
        public static string ReportsDirectory => ConfigurationManager.AppSettings["ReportsDirectory"] ?? 
            System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SupermarketReports");

        /// <summary>
        /// Répertoire des images de produits
        /// </summary>
        public static string ProductImagesDirectory => ConfigurationManager.AppSettings["ProductImagesDirectory"] ?? 
            System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images", "Products");

        /// <summary>
        /// Répertoire des logos d'entreprise
        /// </summary>
        public static string CompanyLogosDirectory => ConfigurationManager.AppSettings["CompanyLogosDirectory"] ?? 
            System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images", "Company");

        /// <summary>
        /// Nombre maximum d'éléments par page dans les listes
        /// </summary>
        public static int DefaultPageSize
        {
            get
            {
                if (int.TryParse(ConfigurationManager.AppSettings["DefaultPageSize"], out int size))
                    return size;
                return 50; // Valeur par défaut
            }
        }

        /// <summary>
        /// Délai d'expiration de session en minutes
        /// </summary>
        public static int SessionTimeoutMinutes
        {
            get
            {
                if (int.TryParse(ConfigurationManager.AppSettings["SessionTimeoutMinutes"], out int timeout))
                    return timeout;
                return 480; // 8 heures par défaut
            }
        }

        /// <summary>
        /// Active ou désactive les logs de débogage
        /// </summary>
        public static bool EnableDebugLogging
        {
            get
            {
                if (bool.TryParse(ConfigurationManager.AppSettings["EnableDebugLogging"], out bool enable))
                    return enable;
                return false; // Désactivé par défaut
            }
        }

        /// <summary>
        /// Active ou désactive la sauvegarde automatique
        /// </summary>
        public static bool EnableAutoBackup
        {
            get
            {
                if (bool.TryParse(ConfigurationManager.AppSettings["EnableAutoBackup"], out bool enable))
                    return enable;
                return true; // Activé par défaut
            }
        }

        /// <summary>
        /// Fréquence de sauvegarde automatique en heures
        /// </summary>
        public static int AutoBackupIntervalHours
        {
            get
            {
                if (int.TryParse(ConfigurationManager.AppSettings["AutoBackupIntervalHours"], out int interval))
                    return interval;
                return 24; // Toutes les 24 heures par défaut
            }
        }
    }
}
