using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using SupermarketManagement.Data;
using SupermarketManagement.Models;
using SupermarketManagement.Repositories;

namespace SupermarketManagement.Services
{
    /// <summary>
    /// Interface pour le service de vente
    /// </summary>
    public interface IVenteService : IService
    {
        /// <summary>
        /// Crée une nouvelle facture de vente
        /// </summary>
        /// <param name="facture">Facture à créer</param>
        /// <param name="details">Détails de la facture</param>
        /// <returns>Résultat avec l'ID de la facture créée</returns>
        Task<ServiceResult<int>> CreerFactureAsync(FactureVente facture, List<DetailFactureVente> details);

        /// <summary>
        /// Valide une facture
        /// </summary>
        /// <param name="factureId">ID de la facture</param>
        /// <returns>Résultat de l'opération</returns>
        Task<ServiceResult> ValiderFactureAsync(int factureId);

        /// <summary>
        /// Annule une facture
        /// </summary>
        /// <param name="factureId">ID de la facture</param>
        /// <param name="motif">Motif d'annulation</param>
        /// <returns>Résultat de l'opération</returns>
        Task<ServiceResult> AnnulerFactureAsync(int factureId, string motif);

        /// <summary>
        /// Calcule les totaux d'une facture
        /// </summary>
        /// <param name="details">Détails de la facture</param>
        /// <returns>Totaux calculés</returns>
        Task<ServiceResult<TotauxFacture>> CalculerTotauxFactureAsync(List<DetailFactureVente> details);

        /// <summary>
        /// Vérifie la disponibilité du stock pour une vente
        /// </summary>
        /// <param name="details">Détails de la vente</param>
        /// <returns>Résultat de la vérification</returns>
        Task<ServiceResult> VerifierDisponibiliteStockAsync(List<DetailFactureVente> details);

        /// <summary>
        /// Génère un numéro de facture
        /// </summary>
        /// <param name="typeFacture">Type de facture</param>
        /// <returns>Numéro de facture généré</returns>
        Task<ServiceResult<string>> GenererNumeroFactureAsync(TypeFacture typeFacture);

        /// <summary>
        /// Obtient les statistiques de vente
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Statistiques de vente</returns>
        Task<ServiceResult<StatistiquesVente>> ObtenirStatistiquesAsync(DateTime dateDebut, DateTime dateFin);
    }

    /// <summary>
    /// Service de gestion des ventes
    /// </summary>
    public class VenteService : IVenteService
    {
        private readonly IFactureVenteRepository _factureRepository;
        private readonly IProduitRepository _produitRepository;
        private readonly IClientRepository _clientRepository;
        private readonly ICalculationService _calculationService;
        private readonly DapperContext _context;

        /// <summary>
        /// Constructeur
        /// </summary>
        public VenteService(
            IFactureVenteRepository factureRepository,
            IProduitRepository produitRepository,
            IClientRepository clientRepository,
            ICalculationService calculationService,
            DapperContext context)
        {
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _produitRepository = produitRepository ?? throw new ArgumentNullException(nameof(produitRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _calculationService = calculationService ?? throw new ArgumentNullException(nameof(calculationService));
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// Initialise le service
        /// </summary>
        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Crée une nouvelle facture de vente
        /// </summary>
        public async Task<ServiceResult<int>> CreerFactureAsync(FactureVente facture, List<DetailFactureVente> details)
        {
            try
            {
                // Validation des données
                var validationResult = await ValiderDonneesFactureAsync(facture, details);
                if (!validationResult.Success)
                    return ServiceResult<int>.CreateError(validationResult.Errors, "Erreur de validation");

                // Vérification du stock
                var stockResult = await VerifierDisponibiliteStockAsync(details);
                if (!stockResult.Success)
                    return ServiceResult<int>.CreateError(stockResult.Message);

                // Calcul des totaux
                var totauxResult = await CalculerTotauxFactureAsync(details);
                if (!totauxResult.Success)
                    return ServiceResult<int>.CreateError(totauxResult.Message);

                // Mise à jour des totaux de la facture
                var totaux = totauxResult.Data;
                facture.SousTotalHT = totaux.SousTotalHT;
                facture.TotalRemise = totaux.TotalRemise;
                facture.TotalTVA = totaux.TotalTVA;
                facture.TotalTTC = totaux.TotalTTC;

                // Génération du numéro de facture si nécessaire
                if (string.IsNullOrEmpty(facture.NumeroFacture))
                {
                    var numeroResult = await GenererNumeroFactureAsync(facture.TypeFacture);
                    if (!numeroResult.Success)
                        return ServiceResult<int>.CreateError(numeroResult.Message);
                    
                    facture.NumeroFacture = numeroResult.Data;
                }

                // Création de la facture avec transaction
                var transaction = await _context.BeginTransactionAsync();
                try
                {
                    var factureId = await _factureRepository.AddWithDetailsAsync(facture, details, transaction);

                    // Si la facture est validée, mettre à jour le stock
                    if (facture.Statut == StatutFacture.Validee)
                    {
                        await MettreAJourStockAsync(details, transaction);
                        await MettreAJourSoldeClientAsync(facture.ClientId, facture.TotalTTC, transaction);
                    }

                    await _factureRepository.CommitTransactionAsync(transaction);
                    
                    return ServiceResult<int>.CreateSuccess(factureId, "Facture créée avec succès");
                }
                catch (Exception ex)
                {
                    await _factureRepository.RollbackTransactionAsync(transaction);
                    throw;
                }
                finally
                {
                    transaction?.Dispose();
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.CreateError($"Erreur lors de la création de la facture : {ex.Message}", "CREATE_INVOICE_ERROR", ex);
            }
        }

        /// <summary>
        /// Valide une facture
        /// </summary>
        public async Task<ServiceResult> ValiderFactureAsync(int factureId)
        {
            try
            {
                var facture = await _factureRepository.GetByIdAsync(factureId);
                if (facture == null)
                    return ServiceResult.CreateError("Facture introuvable");

                if (facture.Statut != StatutFacture.Brouillon)
                    return ServiceResult.CreateError("Seules les factures en brouillon peuvent être validées");

                var details = await _factureRepository.GetDetailsAsync(factureId);
                
                // Vérification du stock
                var stockResult = await VerifierDisponibiliteStockAsync(details.ToList());
                if (!stockResult.Success)
                    return stockResult;

                var transaction = await _context.BeginTransactionAsync();
                try
                {
                    // Mise à jour du statut
                    await _factureRepository.UpdateStatutAsync(factureId, StatutFacture.Validee);

                    // Mise à jour du stock
                    await MettreAJourStockAsync(details.ToList(), transaction);

                    // Mise à jour du solde client
                    await MettreAJourSoldeClientAsync(facture.ClientId, facture.TotalTTC, transaction);

                    await _factureRepository.CommitTransactionAsync(transaction);
                    
                    return ServiceResult.CreateSuccess("Facture validée avec succès");
                }
                catch (Exception ex)
                {
                    await _factureRepository.RollbackTransactionAsync(transaction);
                    throw;
                }
                finally
                {
                    transaction?.Dispose();
                }
            }
            catch (Exception ex)
            {
                return ServiceResult.CreateError($"Erreur lors de la validation : {ex.Message}", "VALIDATE_INVOICE_ERROR", ex);
            }
        }

        /// <summary>
        /// Annule une facture
        /// </summary>
        public async Task<ServiceResult> AnnulerFactureAsync(int factureId, string motif)
        {
            try
            {
                var facture = await _factureRepository.GetByIdAsync(factureId);
                if (facture == null)
                    return ServiceResult.CreateError("Facture introuvable");

                if (facture.Statut == StatutFacture.Annulee)
                    return ServiceResult.CreateError("La facture est déjà annulée");

                var transaction = await _context.BeginTransactionAsync();
                try
                {
                    // Si la facture était validée, restaurer le stock
                    if (facture.Statut == StatutFacture.Validee)
                    {
                        var details = await _factureRepository.GetDetailsAsync(factureId);
                        await RestaurerStockAsync(details.ToList(), transaction);
                        await MettreAJourSoldeClientAsync(facture.ClientId, -facture.TotalTTC, transaction);
                    }

                    // Annulation de la facture
                    await _factureRepository.AnnulerFactureAsync(factureId, motif);

                    await _factureRepository.CommitTransactionAsync(transaction);
                    
                    return ServiceResult.CreateSuccess("Facture annulée avec succès");
                }
                catch (Exception ex)
                {
                    await _factureRepository.RollbackTransactionAsync(transaction);
                    throw;
                }
                finally
                {
                    transaction?.Dispose();
                }
            }
            catch (Exception ex)
            {
                return ServiceResult.CreateError($"Erreur lors de l'annulation : {ex.Message}", "CANCEL_INVOICE_ERROR", ex);
            }
        }

        /// <summary>
        /// Calcule les totaux d'une facture
        /// </summary>
        public async Task<ServiceResult<TotauxFacture>> CalculerTotauxFactureAsync(List<DetailFactureVente> details)
        {
            try
            {
                var totaux = new TotauxFacture();

                foreach (var detail in details)
                {
                    // Calcul du prix unitaire après remise
                    var remise = _calculationService.CalculerRemise(detail.PrixUnitaireHT, detail.RemisePourcentage, detail.RemiseMontant);
                    var prixUnitaireApresRemise = detail.PrixUnitaireHT - remise;

                    // Calcul des montants HT
                    detail.SousTotalHT = _calculationService.ArrondirMontant(detail.Quantite * prixUnitaireApresRemise);
                    
                    // Calcul de la TVA
                    detail.MontantTVA = _calculationService.CalculerMontantTVA(detail.SousTotalHT, detail.TauxTVA);
                    
                    // Calcul du montant TTC
                    detail.SousTotalTTC = detail.SousTotalHT + detail.MontantTVA;
                    detail.PrixUnitaireTTC = _calculationService.CalculerMontantTTC(prixUnitaireApresRemise, detail.TauxTVA);

                    // Accumulation des totaux
                    totaux.SousTotalHT += detail.SousTotalHT;
                    totaux.TotalRemise += remise * detail.Quantite;
                    totaux.TotalTVA += detail.MontantTVA;
                    totaux.TotalTTC += detail.SousTotalTTC;
                }

                return ServiceResult<TotauxFacture>.CreateSuccess(totaux);
            }
            catch (Exception ex)
            {
                return ServiceResult<TotauxFacture>.CreateError($"Erreur lors du calcul des totaux : {ex.Message}", "CALCULATE_TOTALS_ERROR", ex);
            }
        }

        /// <summary>
        /// Vérifie la disponibilité du stock pour une vente
        /// </summary>
        public async Task<ServiceResult> VerifierDisponibiliteStockAsync(List<DetailFactureVente> details)
        {
            try
            {
                var erreurs = new List<string>();

                foreach (var detail in details)
                {
                    var produit = await _produitRepository.GetByIdAsync(detail.ProduitId);
                    if (produit == null)
                    {
                        erreurs.Add($"Produit avec ID {detail.ProduitId} introuvable");
                        continue;
                    }

                    if (!produit.Actif)
                    {
                        erreurs.Add($"Le produit {produit.Nom} n'est pas actif");
                        continue;
                    }

                    var stockDisponible = produit.StockActuel - produit.StockReserve;
                    if (stockDisponible < detail.Quantite)
                    {
                        erreurs.Add($"Stock insuffisant pour {produit.Nom}. Disponible: {stockDisponible}, Demandé: {detail.Quantite}");
                    }
                }

                if (erreurs.Any())
                    return ServiceResult.CreateError(erreurs, "Problèmes de stock détectés");

                return ServiceResult.CreateSuccess("Stock disponible");
            }
            catch (Exception ex)
            {
                return ServiceResult.CreateError($"Erreur lors de la vérification du stock : {ex.Message}", "CHECK_STOCK_ERROR", ex);
            }
        }

        /// <summary>
        /// Génère un numéro de facture
        /// </summary>
        public async Task<ServiceResult<string>> GenererNumeroFactureAsync(TypeFacture typeFacture)
        {
            try
            {
                var numero = await _factureRepository.GenerateNextNumeroAsync(typeFacture);
                return ServiceResult<string>.CreateSuccess(numero);
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.CreateError($"Erreur lors de la génération du numéro : {ex.Message}", "GENERATE_NUMBER_ERROR", ex);
            }
        }

        /// <summary>
        /// Obtient les statistiques de vente
        /// </summary>
        public async Task<ServiceResult<StatistiquesVente>> ObtenirStatistiquesAsync(DateTime dateDebut, DateTime dateFin)
        {
            try
            {
                var statistiques = await _factureRepository.GetStatistiquesJourAsync(dateDebut);
                return ServiceResult<StatistiquesVente>.CreateSuccess(statistiques);
            }
            catch (Exception ex)
            {
                return ServiceResult<StatistiquesVente>.CreateError($"Erreur lors du calcul des statistiques : {ex.Message}", "GET_STATS_ERROR", ex);
            }
        }

        /// <summary>
        /// Valide les données d'une facture
        /// </summary>
        private async Task<ServiceResult> ValiderDonneesFactureAsync(FactureVente facture, List<DetailFactureVente> details)
        {
            var erreurs = new List<string>();

            // Validation de la facture
            if (facture.ClientId <= 0)
                erreurs.Add("Client requis");

            if (facture.UtilisateurId <= 0)
                erreurs.Add("Utilisateur requis");

            if (facture.DateFacture == default)
                erreurs.Add("Date de facture requise");

            // Validation des détails
            if (!details.Any())
                erreurs.Add("Au moins un article est requis");

            foreach (var detail in details)
            {
                if (detail.ProduitId <= 0)
                    erreurs.Add("Produit requis pour chaque ligne");

                if (detail.Quantite <= 0)
                    erreurs.Add("Quantité doit être positive");

                if (detail.PrixUnitaireHT < 0)
                    erreurs.Add("Prix unitaire ne peut pas être négatif");
            }

            // Vérification de l'existence du client
            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null)
                erreurs.Add("Client introuvable");

            if (erreurs.Any())
                return ServiceResult.CreateError(erreurs);

            return ServiceResult.CreateSuccess();
        }

        /// <summary>
        /// Met à jour le stock après une vente
        /// </summary>
        private async Task MettreAJourStockAsync(List<DetailFactureVente> details, IDbTransaction transaction)
        {
            foreach (var detail in details)
            {
                await _produitRepository.UpdateStockAsync(detail.ProduitId, detail.Quantite, false);
            }
        }

        /// <summary>
        /// Restaure le stock après annulation
        /// </summary>
        private async Task RestaurerStockAsync(List<DetailFactureVente> details, IDbTransaction transaction)
        {
            foreach (var detail in details)
            {
                await _produitRepository.UpdateStockAsync(detail.ProduitId, detail.Quantite, true);
            }
        }

        /// <summary>
        /// Met à jour le solde client
        /// </summary>
        private async Task MettreAJourSoldeClientAsync(int clientId, decimal montant, IDbTransaction transaction)
        {
            await _clientRepository.UpdateSoldeAsync(clientId, montant);
        }

        /// <summary>
        /// Libère les ressources
        /// </summary>
        public void Dispose()
        {
            // Rien à libérer spécifiquement
        }
    }

    /// <summary>
    /// Classe pour les totaux d'une facture
    /// </summary>
    public class TotauxFacture
    {
        public decimal SousTotalHT { get; set; }
        public decimal TotalRemise { get; set; }
        public decimal TotalTVA { get; set; }
        public decimal TotalTTC { get; set; }
    }
}
