using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace SupermarketManagement.Repositories
{
    /// <summary>
    /// Interface générique pour les repositories
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public interface IRepository<T> where T : class
    {
        /// <summary>
        /// Obtient toutes les entités
        /// </summary>
        /// <returns>Liste des entités</returns>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// Obtient une entité par son ID
        /// </summary>
        /// <param name="id">ID de l'entité</param>
        /// <returns>Entité ou null</returns>
        Task<T> GetByIdAsync(int id);

        /// <summary>
        /// Ajoute une nouvelle entité
        /// </summary>
        /// <param name="entity">Entité à ajouter</param>
        /// <returns>ID de l'entité créée</returns>
        Task<int> AddAsync(T entity);

        /// <summary>
        /// Met à jour une entité existante
        /// </summary>
        /// <param name="entity">Entité à mettre à jour</param>
        /// <returns>True si la mise à jour réussit</returns>
        Task<bool> UpdateAsync(T entity);

        /// <summary>
        /// Supprime une entité par son ID
        /// </summary>
        /// <param name="id">ID de l'entité à supprimer</param>
        /// <returns>True si la suppression réussit</returns>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// Vérifie si une entité existe
        /// </summary>
        /// <param name="id">ID de l'entité</param>
        /// <returns>True si l'entité existe</returns>
        Task<bool> ExistsAsync(int id);

        /// <summary>
        /// Compte le nombre total d'entités
        /// </summary>
        /// <returns>Nombre d'entités</returns>
        Task<int> CountAsync();
    }

    /// <summary>
    /// Interface pour les repositories avec pagination
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public interface IPagedRepository<T> : IRepository<T> where T : class
    {
        /// <summary>
        /// Obtient une page d'entités
        /// </summary>
        /// <param name="pageNumber">Numéro de page (commence à 1)</param>
        /// <param name="pageSize">Taille de la page</param>
        /// <returns>Page d'entités</returns>
        Task<PagedResult<T>> GetPagedAsync(int pageNumber, int pageSize);

        /// <summary>
        /// Recherche des entités avec pagination
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <param name="pageNumber">Numéro de page</param>
        /// <param name="pageSize">Taille de la page</param>
        /// <returns>Page d'entités correspondant à la recherche</returns>
        Task<PagedResult<T>> SearchPagedAsync(string searchTerm, int pageNumber, int pageSize);
    }

    /// <summary>
    /// Interface pour les repositories avec des entités activables
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public interface IActivatableRepository<T> : IPagedRepository<T> where T : class
    {
        /// <summary>
        /// Obtient toutes les entités actives
        /// </summary>
        /// <returns>Liste des entités actives</returns>
        Task<IEnumerable<T>> GetActiveAsync();

        /// <summary>
        /// Active ou désactive une entité
        /// </summary>
        /// <param name="id">ID de l'entité</param>
        /// <param name="active">État d'activation</param>
        /// <returns>True si l'opération réussit</returns>
        Task<bool> SetActiveAsync(int id, bool active);
    }

    /// <summary>
    /// Résultat paginé
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public class PagedResult<T>
    {
        /// <summary>
        /// Données de la page
        /// </summary>
        public IEnumerable<T> Data { get; set; }

        /// <summary>
        /// Nombre total d'éléments
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Numéro de page actuel
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// Taille de la page
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Nombre total de pages
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// Indique s'il y a une page précédente
        /// </summary>
        public bool HasPreviousPage => PageNumber > 1;

        /// <summary>
        /// Indique s'il y a une page suivante
        /// </summary>
        public bool HasNextPage => PageNumber < TotalPages;

        /// <summary>
        /// Index du premier élément de la page
        /// </summary>
        public int FirstItemIndex => (PageNumber - 1) * PageSize + 1;

        /// <summary>
        /// Index du dernier élément de la page
        /// </summary>
        public int LastItemIndex => Math.Min(PageNumber * PageSize, TotalCount);
    }

    /// <summary>
    /// Interface pour les repositories avec transaction
    /// </summary>
    public interface ITransactionalRepository
    {
        /// <summary>
        /// Commence une transaction
        /// </summary>
        /// <returns>Transaction</returns>
        Task<IDbTransaction> BeginTransactionAsync();

        /// <summary>
        /// Valide une transaction
        /// </summary>
        /// <param name="transaction">Transaction à valider</param>
        Task CommitTransactionAsync(IDbTransaction transaction);

        /// <summary>
        /// Annule une transaction
        /// </summary>
        /// <param name="transaction">Transaction à annuler</param>
        Task RollbackTransactionAsync(IDbTransaction transaction);
    }
}
