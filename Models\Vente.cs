using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Énumération des types de facture
    /// </summary>
    public enum TypeFacture
    {
        Vente,
        Proforma,
        BonLivraison
    }

    /// <summary>
    /// Énumération des modes de paiement
    /// </summary>
    public enum ModePaiement
    {
        Especes,
        Cheque,
        Virement,
        Carte,
        Credit,
        Mixte
    }

    /// <summary>
    /// Énumération des statuts de facture
    /// </summary>
    public enum StatutFacture
    {
        Brouillon,
        Validee,
        Payee,
        Annulee
    }

    /// <summary>
    /// Modèle pour les factures de vente
    /// </summary>
    [Table("factures_vente")]
    public class FactureVente : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Column("numero_facture")]
        public string NumeroFacture { get; set; }

        [Required]
        [Column("client_id")]
        public int ClientId { get; set; }

        [Required]
        [Column("utilisateur_id")]
        public int UtilisateurId { get; set; }

        [Required]
        [Column("date_facture")]
        public DateTime DateFacture { get; set; } = DateTime.Today;

        [Required]
        [Column("heure_facture")]
        public TimeSpan HeureFacture { get; set; } = DateTime.Now.TimeOfDay;

        [Column("type_facture")]
        public TypeFacture TypeFacture { get; set; } = TypeFacture.Vente;

        [Required]
        [Column("sous_total_ht")]
        public decimal SousTotalHT { get; set; } = 0.00m;

        [Column("total_remise")]
        public decimal TotalRemise { get; set; } = 0.00m;

        [Required]
        [Column("total_tva")]
        public decimal TotalTVA { get; set; } = 0.00m;

        [Required]
        [Column("total_ttc")]
        public decimal TotalTTC { get; set; } = 0.00m;

        [Column("mode_paiement")]
        public ModePaiement ModePaiement { get; set; } = ModePaiement.Especes;

        [Column("montant_paye")]
        public decimal MontantPaye { get; set; } = 0.00m;

        [Column("montant_rendu")]
        public decimal MontantRendu { get; set; } = 0.00m;

        [Column("statut")]
        public StatutFacture Statut { get; set; } = StatutFacture.Brouillon;

        [Column("notes")]
        public string Notes { get; set; }

        [Column("caisse_id")]
        public int? CaisseId { get; set; }

        [Column("banque_id")]
        public int? BanqueId { get; set; }

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        [Column("date_modification")]
        public new DateTime DateModification { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Client Client { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual Caisse Caisse { get; set; }
        public virtual Banque Banque { get; set; }
        public virtual ICollection<DetailFactureVente> Details { get; set; } = new List<DetailFactureVente>();

        // Propriétés calculées
        [NotMapped]
        public decimal MontantRestant => TotalTTC - MontantPaye;

        [NotMapped]
        public bool EstPayee => MontantRestant <= 0;

        [NotMapped]
        public int NombreArticles => Details?.Sum(d => (int)d.Quantite) ?? 0;

        [NotMapped]
        public string StatutPaiement
        {
            get
            {
                if (MontantPaye == 0) return "Non payée";
                if (MontantRestant <= 0) return "Payée";
                return "Partiellement payée";
            }
        }
    }

    /// <summary>
    /// Modèle pour les détails de facture de vente
    /// </summary>
    [Table("details_facture_vente")]
    public class DetailFactureVente : BaseEntity
    {
        [Required]
        [Column("facture_id")]
        public int FactureId { get; set; }

        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Required]
        [Column("unite_id")]
        public int UniteId { get; set; }

        [Required]
        [Column("quantite")]
        public decimal Quantite { get; set; }

        [Required]
        [Column("prix_unitaire_ht")]
        public decimal PrixUnitaireHT { get; set; }

        [Required]
        [Column("prix_unitaire_ttc")]
        public decimal PrixUnitaireTTC { get; set; }

        [Column("remise_pourcentage")]
        public decimal RemisePourcentage { get; set; } = 0.00m;

        [Column("remise_montant")]
        public decimal RemiseMontant { get; set; } = 0.00m;

        [Required]
        [Column("taux_tva")]
        public decimal TauxTVA { get; set; }

        [Required]
        [Column("montant_tva")]
        public decimal MontantTVA { get; set; }

        [Required]
        [Column("sous_total_ht")]
        public decimal SousTotalHT { get; set; }

        [Required]
        [Column("sous_total_ttc")]
        public decimal SousTotalTTC { get; set; }

        // Navigation properties
        public virtual FactureVente Facture { get; set; }
        public virtual Produit Produit { get; set; }
        public virtual UniteMesure Unite { get; set; }

        // Propriétés calculées
        [NotMapped]
        public decimal PrixUnitaireApresRemise => PrixUnitaireHT - (PrixUnitaireHT * RemisePourcentage / 100) - RemiseMontant;

        [NotMapped]
        public decimal TotalRemiseLigne => (PrixUnitaireHT * RemisePourcentage / 100 * Quantite) + RemiseMontant;
    }

    /// <summary>
    /// Énumération des statuts de retour
    /// </summary>
    public enum StatutRetour
    {
        EnAttente,
        Validee,
        Annulee
    }

    /// <summary>
    /// Modèle pour les retours clients
    /// </summary>
    [Table("retours_client")]
    public class RetourClient : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Column("numero_retour")]
        public string NumeroRetour { get; set; }

        [Column("facture_vente_id")]
        public int? FactureVenteId { get; set; }

        [Required]
        [Column("client_id")]
        public int ClientId { get; set; }

        [Required]
        [Column("utilisateur_id")]
        public int UtilisateurId { get; set; }

        [Required]
        [Column("date_retour")]
        public DateTime DateRetour { get; set; } = DateTime.Today;

        [Column("motif")]
        public string Motif { get; set; }

        [Required]
        [Column("total_retour")]
        public decimal TotalRetour { get; set; } = 0.00m;

        [Column("statut")]
        public StatutRetour Statut { get; set; } = StatutRetour.EnAttente;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual FactureVente FactureVente { get; set; }
        public virtual Client Client { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual ICollection<DetailRetourClient> Details { get; set; } = new List<DetailRetourClient>();
    }

    /// <summary>
    /// Modèle pour les détails de retour client
    /// </summary>
    [Table("details_retour_client")]
    public class DetailRetourClient : BaseEntity
    {
        [Required]
        [Column("retour_id")]
        public int RetourId { get; set; }

        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Required]
        [Column("unite_id")]
        public int UniteId { get; set; }

        [Required]
        [Column("quantite_retournee")]
        public decimal QuantiteRetournee { get; set; }

        [Required]
        [Column("prix_unitaire")]
        public decimal PrixUnitaire { get; set; }

        [Required]
        [Column("sous_total")]
        public decimal SousTotal { get; set; }

        // Navigation properties
        public virtual RetourClient Retour { get; set; }
        public virtual Produit Produit { get; set; }
        public virtual UniteMesure Unite { get; set; }
    }
}
