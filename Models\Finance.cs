using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Modèle pour les caisses
    /// </summary>
    [Table("caisses")]
    public class Caisse : BaseEntity, IActivatable, INamedEntity
    {
        [Required]
        [StringLength(255)]
        [Column("nom")]
        public string Nom { get; set; }

        [Column("description")]
        public string Description { get; set; }

        [Column("solde_initial")]
        public decimal SoldeInitial { get; set; } = 0.00m;

        [Column("solde_actuel")]
        public decimal SoldeActuel { get; set; } = 0.00m;

        [StringLength(10)]
        [Column("devise")]
        public string Devise { get; set; } = "DZD";

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Modèle pour les banques
    /// </summary>
    [Table("banques")]
    public class Banque : BaseEntity, IActivatable, INamedEntity
    {
        [Required]
        [StringLength(255)]
        [Column("nom_banque")]
        public string Nom { get; set; }

        [Required]
        [StringLength(100)]
        [Column("numero_compte")]
        public string NumeroCompte { get; set; }

        [StringLength(50)]
        [Column("rib")]
        public string RIB { get; set; }

        [StringLength(20)]
        [Column("swift")]
        public string SWIFT { get; set; }

        [Column("solde_initial")]
        public decimal SoldeInitial { get; set; } = 0.00m;

        [Column("solde_actuel")]
        public decimal SoldeActuel { get; set; } = 0.00m;

        [StringLength(10)]
        [Column("devise")]
        public string Devise { get; set; } = "DZD";

        [Column("actif")]
        public bool Actif { get; set; } = true;

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Énumération des types de mouvement
    /// </summary>
    public enum TypeMouvement
    {
        Entree,
        Sortie
    }

    /// <summary>
    /// Énumération des types de document
    /// </summary>
    public enum TypeDocument
    {
        Vente,
        Achat,
        Depense,
        Recette,
        PaiementClient,
        PaiementFournisseur,
        Autre
    }

    /// <summary>
    /// Modèle pour les mouvements de caisse
    /// </summary>
    [Table("mouvements_caisse")]
    public class MouvementCaisse : BaseEntity
    {
        [Required]
        [Column("caisse_id")]
        public int CaisseId { get; set; }

        [Required]
        [Column("type_mouvement")]
        public TypeMouvement TypeMouvement { get; set; }

        [Required]
        [Column("montant")]
        public decimal Montant { get; set; }

        [Required]
        [StringLength(255)]
        [Column("libelle")]
        public string Libelle { get; set; }

        [StringLength(100)]
        [Column("reference_document")]
        public string ReferenceDocument { get; set; }

        [Required]
        [Column("type_document")]
        public TypeDocument TypeDocument { get; set; }

        [Column("document_id")]
        public int? DocumentId { get; set; }

        [Required]
        [Column("solde_avant")]
        public decimal SoldeAvant { get; set; }

        [Required]
        [Column("solde_apres")]
        public decimal SoldeApres { get; set; }

        [Required]
        [Column("utilisateur_id")]
        public int UtilisateurId { get; set; }

        [Column("date_mouvement")]
        public DateTime DateMouvement { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Caisse Caisse { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
    }

    /// <summary>
    /// Modèle pour les mouvements de banque
    /// </summary>
    [Table("mouvements_banque")]
    public class MouvementBanque : BaseEntity
    {
        [Required]
        [Column("banque_id")]
        public int BanqueId { get; set; }

        [Required]
        [Column("type_mouvement")]
        public TypeMouvement TypeMouvement { get; set; }

        [Required]
        [Column("montant")]
        public decimal Montant { get; set; }

        [Required]
        [StringLength(255)]
        [Column("libelle")]
        public string Libelle { get; set; }

        [StringLength(100)]
        [Column("reference_document")]
        public string ReferenceDocument { get; set; }

        [Required]
        [Column("type_document")]
        public TypeDocument TypeDocument { get; set; }

        [Column("document_id")]
        public int? DocumentId { get; set; }

        [Required]
        [Column("solde_avant")]
        public decimal SoldeAvant { get; set; }

        [Required]
        [Column("solde_apres")]
        public decimal SoldeApres { get; set; }

        [Required]
        [Column("utilisateur_id")]
        public int UtilisateurId { get; set; }

        [Column("date_mouvement")]
        public DateTime DateMouvement { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Banque Banque { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
    }
}
