using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using SupermarketManagement.Configuration;
using SupermarketManagement.Data;
using SupermarketManagement.Forms;
using SupermarketManagement.Repositories;
using SupermarketManagement.Services;

namespace SupermarketManagement
{
    /// <summary>
    /// Point d'entrée principal de l'application
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// Point d'entrée principal de l'application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Configuration de l'application Windows Forms
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.SetHighDpiMode(HighDpiMode.SystemAware);

            try
            {
                // Configuration des services
                var services = ConfigureServices();
                var serviceProvider = services.BuildServiceProvider();

                // Vérification de la connexion à la base de données
                if (!VerifierConnexionBD())
                {
                    MessageBox.Show(
                        "Impossible de se connecter à la base de données.\n" +
                        "Veuillez vérifier la configuration dans App.config.",
                        "Erreur de connexion",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                    return;
                }

                // Authentification de l'utilisateur
                var utilisateurConnecte = AuthentifierUtilisateur(serviceProvider);
                if (utilisateurConnecte == null)
                {
                    return; // L'utilisateur a annulé la connexion
                }

                // Démarrage de l'application principale
                var mainForm = new MainForm(utilisateurConnecte, serviceProvider);
                Application.Run(mainForm);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Erreur fatale lors du démarrage de l'application :\n{ex.Message}",
                    "Erreur",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Configure les services de l'application
        /// </summary>
        /// <returns>Collection de services configurée</returns>
        private static IServiceCollection ConfigureServices()
        {
            var services = new ServiceCollection();

            // Configuration de la base de données
            services.AddSingleton<DapperContext>();

            // Repositories
            services.AddScoped<IClientRepository, ClientRepository>();
            services.AddScoped<IProduitRepository, ProduitRepository>();
            services.AddScoped<IFactureVenteRepository, FactureVenteRepository>();
            // Ajouter d'autres repositories selon les besoins

            // Services métier
            services.AddScoped<ICalculationService, CalculationService>();
            services.AddScoped<IVenteService, VenteService>();
            services.AddScoped<INotificationService, NotificationService>();
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            // Ajouter d'autres services selon les besoins

            return services;
        }

        /// <summary>
        /// Vérifie la connexion à la base de données
        /// </summary>
        /// <returns>True si la connexion réussit</returns>
        private static bool VerifierConnexionBD()
        {
            try
            {
                return DatabaseConfig.TestConnection();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Erreur lors de la vérification de la connexion :\n{ex.Message}",
                    "Erreur de connexion",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Authentifie l'utilisateur
        /// </summary>
        /// <param name="serviceProvider">Fournisseur de services</param>
        /// <returns>Utilisateur authentifié ou null</returns>
        private static Models.Utilisateur AuthentifierUtilisateur(IServiceProvider serviceProvider)
        {
            try
            {
                var authService = serviceProvider.GetRequiredService<IAuthenticationService>();
                var loginForm = new LoginForm(authService);

                if (loginForm.ShowDialog() == DialogResult.OK)
                {
                    return loginForm.UtilisateurConnecte;
                }

                return null;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Erreur lors de l'authentification :\n{ex.Message}",
                    "Erreur d'authentification",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
                return null;
            }
        }
    }

    /// <summary>
    /// Service de notification pour Windows Forms
    /// </summary>
    public class NotificationService : INotificationService
    {
        public void ShowInfo(string titre, string message)
        {
            MessageBox.Show(message, titre, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        public void ShowSuccess(string titre, string message)
        {
            MessageBox.Show(message, titre, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        public void ShowWarning(string titre, string message)
        {
            MessageBox.Show(message, titre, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        public void ShowError(string titre, string message)
        {
            MessageBox.Show(message, titre, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        public bool ShowConfirmation(string titre, string message)
        {
            var result = MessageBox.Show(message, titre, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            return result == DialogResult.Yes;
        }
    }

    /// <summary>
    /// Service d'authentification simple
    /// </summary>
    public class AuthenticationService : IAuthenticationService
    {
        private readonly DapperContext _context;
        private Models.Utilisateur _utilisateurActuel;

        public AuthenticationService(DapperContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public Models.Utilisateur UtilisateurActuel => _utilisateurActuel;

        public async System.Threading.Tasks.Task<ServiceResult<Models.Utilisateur>> AuthenticateAsync(string nomUtilisateur, string motDePasse)
        {
            try
            {
                // Recherche de l'utilisateur dans la base de données
                var sql = "SELECT * FROM utilisateurs WHERE nom_utilisateur = @NomUtilisateur AND actif = 1";
                var utilisateur = await _context.QueryFirstOrDefaultAsync<Models.Utilisateur>(sql, new { NomUtilisateur = nomUtilisateur });

                if (utilisateur == null)
                {
                    return ServiceResult<Models.Utilisateur>.CreateError("Nom d'utilisateur ou mot de passe incorrect");
                }

                // Vérification du mot de passe (ici simplifié - en production, utiliser un hash)
                if (!VerifierMotDePasse(motDePasse, utilisateur.MotDePasse))
                {
                    return ServiceResult<Models.Utilisateur>.CreateError("Nom d'utilisateur ou mot de passe incorrect");
                }

                // Mise à jour de la dernière connexion
                await _context.ExecuteAsync(
                    "UPDATE utilisateurs SET derniere_connexion = @Date WHERE id = @Id",
                    new { Date = DateTime.Now, Id = utilisateur.Id });

                // Enregistrement de la session
                await _context.ExecuteAsync(
                    "INSERT INTO sessions_utilisateurs (utilisateur_id, date_connexion, adresse_ip, statut) VALUES (@UtilisateurId, @Date, @IP, @Statut)",
                    new { UtilisateurId = utilisateur.Id, Date = DateTime.Now, IP = "127.0.0.1", Statut = "active" });

                _utilisateurActuel = utilisateur;
                return ServiceResult<Models.Utilisateur>.CreateSuccess(utilisateur, "Connexion réussie");
            }
            catch (Exception ex)
            {
                return ServiceResult<Models.Utilisateur>.CreateError($"Erreur lors de l'authentification : {ex.Message}");
            }
        }

        public async System.Threading.Tasks.Task LogoutAsync()
        {
            if (_utilisateurActuel != null)
            {
                // Fermer la session active
                await _context.ExecuteAsync(
                    "UPDATE sessions_utilisateurs SET date_deconnexion = @Date, statut = @Statut WHERE utilisateur_id = @UtilisateurId AND statut = 'active'",
                    new { Date = DateTime.Now, Statut = "fermee", UtilisateurId = _utilisateurActuel.Id });

                _utilisateurActuel = null;
            }
        }

        /// <summary>
        /// Vérifie le mot de passe (version simplifiée)
        /// </summary>
        /// <param name="motDePasseSaisi">Mot de passe saisi</param>
        /// <param name="motDePasseStocke">Mot de passe stocké</param>
        /// <returns>True si le mot de passe est correct</returns>
        private bool VerifierMotDePasse(string motDePasseSaisi, string motDePasseStocke)
        {
            // Version simplifiée - en production, utiliser BCrypt ou similaire
            // Pour le moment, on compare directement (mot de passe par défaut: "password")
            return motDePasseSaisi == "password" || motDePasseSaisi == motDePasseStocke;
        }
    }
}
