using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SupermarketManagement.Models
{
    /// <summary>
    /// Énumération des types de mouvement de stock
    /// </summary>
    public enum TypeMouvementStock
    {
        Entree,
        Sortie,
        Ajustement,
        Transfert,
        Inventaire
    }

    /// <summary>
    /// Modèle pour les mouvements de stock
    /// </summary>
    [Table("mouvements_stock")]
    public class MouvementStock : BaseEntity
    {
        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Required]
        [Column("type_mouvement")]
        public TypeMouvementStock TypeMouvement { get; set; }

        [StringLength(100)]
        [Column("reference_document")]
        public string ReferenceDocument { get; set; }

        [Required]
        [Column("quantite")]
        public decimal Quantite { get; set; }

        [Column("prix_unitaire")]
        public decimal? PrixUnitaire { get; set; }

        [Required]
        [Column("stock_avant")]
        public decimal StockAvant { get; set; }

        [Required]
        [Column("stock_apres")]
        public decimal StockApres { get; set; }

        [Column("motif")]
        public string Motif { get; set; }

        [Required]
        [Column("utilisateur_id")]
        public int UtilisateurId { get; set; }

        [Column("date_mouvement")]
        public DateTime DateMouvement { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Produit Produit { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }

        // Propriétés calculées
        [NotMapped]
        public decimal ValeurMouvement => (PrixUnitaire ?? 0) * Quantite;

        [NotMapped]
        public string TypeMouvementTexte
        {
            get
            {
                return TypeMouvement switch
                {
                    TypeMouvementStock.Entree => "Entrée",
                    TypeMouvementStock.Sortie => "Sortie",
                    TypeMouvementStock.Ajustement => "Ajustement",
                    TypeMouvementStock.Transfert => "Transfert",
                    TypeMouvementStock.Inventaire => "Inventaire",
                    _ => "Inconnu"
                };
            }
        }
    }

    /// <summary>
    /// Énumération des statuts d'inventaire
    /// </summary>
    public enum StatutInventaire
    {
        EnCours,
        Termine,
        Valide
    }

    /// <summary>
    /// Modèle pour les inventaires
    /// </summary>
    [Table("inventaires")]
    public class Inventaire : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Column("numero_inventaire")]
        public string NumeroInventaire { get; set; }

        [Required]
        [Column("date_inventaire")]
        public DateTime DateInventaire { get; set; } = DateTime.Today;

        [Required]
        [Column("utilisateur_id")]
        public int UtilisateurId { get; set; }

        [Column("statut")]
        public StatutInventaire Statut { get; set; } = StatutInventaire.EnCours;

        [Column("notes")]
        public string Notes { get; set; }

        [Column("date_creation")]
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual ICollection<DetailInventaire> Details { get; set; } = new List<DetailInventaire>();

        // Propriétés calculées
        [NotMapped]
        public decimal TotalEcartValeur => Details?.Sum(d => d.ValeurEcart) ?? 0;

        [NotMapped]
        public int NombreProduitsComptes => Details?.Count ?? 0;

        [NotMapped]
        public int NombreProduitsAvecEcart => Details?.Count(d => d.Ecart != 0) ?? 0;
    }

    /// <summary>
    /// Modèle pour les détails d'inventaire
    /// </summary>
    [Table("details_inventaire")]
    public class DetailInventaire : BaseEntity
    {
        [Required]
        [Column("inventaire_id")]
        public int InventaireId { get; set; }

        [Required]
        [Column("produit_id")]
        public int ProduitId { get; set; }

        [Required]
        [Column("stock_theorique")]
        public decimal StockTheorique { get; set; }

        [Required]
        [Column("stock_physique")]
        public decimal StockPhysique { get; set; }

        [Required]
        [Column("ecart")]
        public decimal Ecart { get; set; }

        [Required]
        [Column("valeur_ecart")]
        public decimal ValeurEcart { get; set; }

        // Navigation properties
        public virtual Inventaire Inventaire { get; set; }
        public virtual Produit Produit { get; set; }

        // Propriétés calculées
        [NotMapped]
        public decimal PourcentageEcart => StockTheorique != 0 ? (Ecart / StockTheorique) * 100 : 0;

        [NotMapped]
        public string TypeEcart
        {
            get
            {
                if (Ecart > 0) return "Excédent";
                if (Ecart < 0) return "Manquant";
                return "Conforme";
            }
        }

        [NotMapped]
        public bool AEcart => Ecart != 0;
    }

    /// <summary>
    /// Vue pour l'état du stock (correspond à la vue SQL)
    /// </summary>
    public class EtatStock
    {
        public int Id { get; set; }
        public string CodeProduit { get; set; }
        public string Nom { get; set; }
        public string Categorie { get; set; }
        public decimal StockActuel { get; set; }
        public decimal StockMin { get; set; }
        public decimal StockMax { get; set; }
        public decimal StockReserve { get; set; }
        public decimal PrixAchatUnitaire { get; set; }
        public decimal PrixVenteUnitaire { get; set; }
        public decimal ValeurStock { get; set; }
        public string StatutStock { get; set; }

        // Propriétés calculées
        [NotMapped]
        public decimal StockDisponible => StockActuel - StockReserve;

        [NotMapped]
        public bool EstEnRupture => StatutStock == "Rupture";

        [NotMapped]
        public bool EstEnAlerte => StatutStock == "Alerte";

        [NotMapped]
        public decimal MargeUnitaire => PrixVenteUnitaire - PrixAchatUnitaire;

        [NotMapped]
        public decimal PourcentageMarge => PrixAchatUnitaire > 0 ? (MargeUnitaire / PrixAchatUnitaire) * 100 : 0;
    }
}
