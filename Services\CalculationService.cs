using System;
using SupermarketManagement.Configuration;

namespace SupermarketManagement.Services
{
    /// <summary>
    /// Service de calculs financiers et commerciaux
    /// </summary>
    public class CalculationService : ICalculationService
    {
        /// <summary>
        /// Calcule le montant TTC à partir du montant HT
        /// </summary>
        /// <param name="montantHT">Montant HT</param>
        /// <param name="tauxTVA">Taux de TVA en pourcentage</param>
        /// <returns>Montant TTC</returns>
        public decimal CalculerMontantTTC(decimal montantHT, decimal tauxTVA)
        {
            if (montantHT < 0)
                throw new ArgumentException("Le montant HT ne peut pas être négatif", nameof(montantHT));
            
            if (tauxTVA < 0)
                throw new ArgumentException("Le taux de TVA ne peut pas être négatif", nameof(tauxTVA));

            var montantTVA = CalculerMontantTVA(montantHT, tauxTVA);
            return ArrondirMontant(montantHT + montantTVA);
        }

        /// <summary>
        /// Calcule le montant HT à partir du montant TTC
        /// </summary>
        /// <param name="montantTTC">Montant TTC</param>
        /// <param name="tauxTVA">Taux de TVA en pourcentage</param>
        /// <returns>Montant HT</returns>
        public decimal CalculerMontantHT(decimal montantTTC, decimal tauxTVA)
        {
            if (montantTTC < 0)
                throw new ArgumentException("Le montant TTC ne peut pas être négatif", nameof(montantTTC));
            
            if (tauxTVA < 0)
                throw new ArgumentException("Le taux de TVA ne peut pas être négatif", nameof(tauxTVA));

            if (tauxTVA == 0)
                return ArrondirMontant(montantTTC);

            var montantHT = montantTTC / (1 + (tauxTVA / 100));
            return ArrondirMontant(montantHT);
        }

        /// <summary>
        /// Calcule le montant de la TVA
        /// </summary>
        /// <param name="montantHT">Montant HT</param>
        /// <param name="tauxTVA">Taux de TVA en pourcentage</param>
        /// <returns>Montant de la TVA</returns>
        public decimal CalculerMontantTVA(decimal montantHT, decimal tauxTVA)
        {
            if (montantHT < 0)
                throw new ArgumentException("Le montant HT ne peut pas être négatif", nameof(montantHT));
            
            if (tauxTVA < 0)
                throw new ArgumentException("Le taux de TVA ne peut pas être négatif", nameof(tauxTVA));

            var montantTVA = montantHT * (tauxTVA / 100);
            return ArrondirMontant(montantTVA);
        }

        /// <summary>
        /// Calcule une remise
        /// </summary>
        /// <param name="montant">Montant de base</param>
        /// <param name="pourcentageRemise">Pourcentage de remise</param>
        /// <param name="montantRemise">Montant de remise fixe</param>
        /// <returns>Montant de la remise</returns>
        public decimal CalculerRemise(decimal montant, decimal pourcentageRemise = 0, decimal montantRemise = 0)
        {
            if (montant < 0)
                throw new ArgumentException("Le montant ne peut pas être négatif", nameof(montant));
            
            if (pourcentageRemise < 0 || pourcentageRemise > 100)
                throw new ArgumentException("Le pourcentage de remise doit être entre 0 et 100", nameof(pourcentageRemise));
            
            if (montantRemise < 0)
                throw new ArgumentException("Le montant de remise ne peut pas être négatif", nameof(montantRemise));

            var remisePourcentage = montant * (pourcentageRemise / 100);
            var remiseTotale = remisePourcentage + montantRemise;
            
            // La remise ne peut pas être supérieure au montant de base
            if (remiseTotale > montant)
                remiseTotale = montant;

            return ArrondirMontant(remiseTotale);
        }

        /// <summary>
        /// Arrondit un montant selon les règles comptables
        /// </summary>
        /// <param name="montant">Montant à arrondir</param>
        /// <param name="decimales">Nombre de décimales</param>
        /// <returns>Montant arrondi</returns>
        public decimal ArrondirMontant(decimal montant, int decimales = 2)
        {
            if (decimales < 0)
                throw new ArgumentException("Le nombre de décimales ne peut pas être négatif", nameof(decimales));

            return Math.Round(montant, decimales, MidpointRounding.AwayFromZero);
        }

        /// <summary>
        /// Calcule le prix unitaire avec remise
        /// </summary>
        /// <param name="prixUnitaire">Prix unitaire de base</param>
        /// <param name="pourcentageRemise">Pourcentage de remise</param>
        /// <param name="montantRemise">Montant de remise fixe</param>
        /// <returns>Prix unitaire après remise</returns>
        public decimal CalculerPrixUnitaireAvecRemise(decimal prixUnitaire, decimal pourcentageRemise = 0, decimal montantRemise = 0)
        {
            var remise = CalculerRemise(prixUnitaire, pourcentageRemise, montantRemise);
            return ArrondirMontant(prixUnitaire - remise);
        }

        /// <summary>
        /// Calcule le sous-total d'une ligne de facture
        /// </summary>
        /// <param name="quantite">Quantité</param>
        /// <param name="prixUnitaire">Prix unitaire</param>
        /// <param name="pourcentageRemise">Pourcentage de remise</param>
        /// <param name="montantRemise">Montant de remise fixe</param>
        /// <returns>Sous-total</returns>
        public decimal CalculerSousTotal(decimal quantite, decimal prixUnitaire, decimal pourcentageRemise = 0, decimal montantRemise = 0)
        {
            if (quantite < 0)
                throw new ArgumentException("La quantité ne peut pas être négative", nameof(quantite));

            var prixAvecRemise = CalculerPrixUnitaireAvecRemise(prixUnitaire, pourcentageRemise, montantRemise);
            return ArrondirMontant(quantite * prixAvecRemise);
        }

        /// <summary>
        /// Calcule le Prix Moyen Pondéré (PMP)
        /// </summary>
        /// <param name="stockActuel">Stock actuel</param>
        /// <param name="pmpActuel">PMP actuel</param>
        /// <param name="quantiteEntree">Quantité en entrée</param>
        /// <param name="prixUnitaireEntree">Prix unitaire de l'entrée</param>
        /// <returns>Nouveau PMP</returns>
        public decimal CalculerNouveauPMP(decimal stockActuel, decimal pmpActuel, decimal quantiteEntree, decimal prixUnitaireEntree)
        {
            if (stockActuel < 0)
                throw new ArgumentException("Le stock actuel ne peut pas être négatif", nameof(stockActuel));
            
            if (quantiteEntree <= 0)
                throw new ArgumentException("La quantité d'entrée doit être positive", nameof(quantiteEntree));
            
            if (prixUnitaireEntree < 0)
                throw new ArgumentException("Le prix unitaire d'entrée ne peut pas être négatif", nameof(prixUnitaireEntree));

            var stockTotal = stockActuel + quantiteEntree;
            
            if (stockTotal == 0)
                return 0;

            var valeurStockActuel = stockActuel * pmpActuel;
            var valeurEntree = quantiteEntree * prixUnitaireEntree;
            var valeurTotale = valeurStockActuel + valeurEntree;

            return ArrondirMontant(valeurTotale / stockTotal);
        }

        /// <summary>
        /// Calcule la marge bénéficiaire
        /// </summary>
        /// <param name="prixVente">Prix de vente</param>
        /// <param name="prixAchat">Prix d'achat</param>
        /// <returns>Marge en montant</returns>
        public decimal CalculerMarge(decimal prixVente, decimal prixAchat)
        {
            if (prixVente < 0)
                throw new ArgumentException("Le prix de vente ne peut pas être négatif", nameof(prixVente));
            
            if (prixAchat < 0)
                throw new ArgumentException("Le prix d'achat ne peut pas être négatif", nameof(prixAchat));

            return ArrondirMontant(prixVente - prixAchat);
        }

        /// <summary>
        /// Calcule le pourcentage de marge
        /// </summary>
        /// <param name="prixVente">Prix de vente</param>
        /// <param name="prixAchat">Prix d'achat</param>
        /// <returns>Pourcentage de marge</returns>
        public decimal CalculerPourcentageMarge(decimal prixVente, decimal prixAchat)
        {
            if (prixAchat == 0)
                return 0;

            var marge = CalculerMarge(prixVente, prixAchat);
            return ArrondirMontant((marge / prixAchat) * 100);
        }

        /// <summary>
        /// Calcule le prix de vente à partir du prix d'achat et de la marge souhaitée
        /// </summary>
        /// <param name="prixAchat">Prix d'achat</param>
        /// <param name="pourcentageMarge">Pourcentage de marge souhaité</param>
        /// <returns>Prix de vente</returns>
        public decimal CalculerPrixVenteAvecMarge(decimal prixAchat, decimal pourcentageMarge)
        {
            if (prixAchat < 0)
                throw new ArgumentException("Le prix d'achat ne peut pas être négatif", nameof(prixAchat));
            
            if (pourcentageMarge < 0)
                throw new ArgumentException("Le pourcentage de marge ne peut pas être négatif", nameof(pourcentageMarge));

            var marge = prixAchat * (pourcentageMarge / 100);
            return ArrondirMontant(prixAchat + marge);
        }

        /// <summary>
        /// Convertit un montant en lettres (pour les factures)
        /// </summary>
        /// <param name="montant">Montant à convertir</param>
        /// <param name="devise">Devise</param>
        /// <returns>Montant en lettres</returns>
        public string ConvertirMontantEnLettres(decimal montant, string devise = "DZD")
        {
            // Implémentation simplifiée - à améliorer selon les besoins
            var partieEntiere = (long)Math.Floor(montant);
            var partieDecimale = (int)Math.Round((montant - partieEntiere) * 100);

            var lettres = ConvertirNombreEnLettres(partieEntiere);
            
            if (partieDecimale > 0)
            {
                lettres += $" et {ConvertirNombreEnLettres(partieDecimale)} centimes";
            }

            return $"{lettres} {GetDeviseEnLettres(devise)}";
        }

        /// <summary>
        /// Convertit un nombre en lettres (implémentation simplifiée)
        /// </summary>
        private string ConvertirNombreEnLettres(long nombre)
        {
            if (nombre == 0) return "zéro";
            
            // Implémentation simplifiée - à compléter selon les besoins
            return nombre.ToString(); // Placeholder
        }

        /// <summary>
        /// Obtient le nom de la devise en lettres
        /// </summary>
        private string GetDeviseEnLettres(string devise)
        {
            return devise switch
            {
                "DZD" => "dinars algériens",
                "EUR" => "euros",
                "USD" => "dollars américains",
                _ => devise
            };
        }
    }
}
